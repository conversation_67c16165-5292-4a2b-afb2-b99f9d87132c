#!/usr/bin/env node
// -*- coding: utf-8 -*-

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const axios = require('axios');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);
const os = require('os');
const { createClient } = require('@supabase/supabase-js');

// 环境自适应：发现基础路径
function discoverBasePath() {
  // 1. 优先使用环境变量
  if (process.env.API_BASE_PATH) {
    return process.env.API_BASE_PATH;
  }
  
  // 2. 从脚本位置推断
  const scriptDir = __dirname;
  const possiblePaths = [
    scriptDir.replace(/\/local-singbox\/scripts$/, ''),
    path.join(scriptDir, '../..'),
    '/home/<USER>/API'
  ];
  
  // 3. 通过特征文件验证路径
  for (const testPath of possiblePaths) {
    if (fs.existsSync(path.join(testPath, 'CLAUDE.md')) || 
        fs.existsSync(path.join(testPath, 'Douyin_TikTok_Download_API'))) {
      return testPath;
    }
  }
  
  // 4. 默认路径
  return '/home/<USER>/API';
}

// 获取基础路径
const BASE_PATH = discoverBasePath();

// 配置文件路径（使用动态基础路径）
const CONFIG_FILE = path.join(BASE_PATH, 'local-singbox/config/integrated_config_multi.yaml');

// 更新配置中的相对路径为绝对路径
function updateConfigPaths(config) {
  // 更新Cookie池文件路径
  if (config.cookie && config.cookie.pool_file && !path.isAbsolute(config.cookie.pool_file)) {
    config.cookie.pool_file = path.join(BASE_PATH, config.cookie.pool_file);
  }
  
  // 更新API实例配置文件路径
  if (config.api && config.api.instances) {
    config.api.instances.forEach(instance => {
      if (instance.config_file && !path.isAbsolute(instance.config_file)) {
        instance.config_file = path.join(BASE_PATH, instance.config_file);
      }
    });
  }
  
  // 更新订阅配置路径
  if (config.subscription && config.subscription.output_path && !path.isAbsolute(config.subscription.output_path)) {
    config.subscription.output_path = path.join(BASE_PATH, config.subscription.output_path);
  }
  
  return config;
}

// 加载配置
let config = yaml.load(fs.readFileSync(CONFIG_FILE, 'utf8'));
config = updateConfigPaths(config);

// 检测是否在systemd环境中运行
const IN_SYSTEMD = process.env.JOURNAL_STREAM !== undefined;

// 日志记录器
class Logger {
  constructor(name = 'integrated-monitor') {
    this.name = name;
  }

  _format(level, message) {
    if (IN_SYSTEMD) {
      return `${level}: ${message}`;
    }
    const timestamp = new Date().toISOString();
    return `${timestamp} - ${level} - ${message}`;
  }

  info(message) {
    console.log(this._format('INFO', message));
  }

  error(message) {
    console.error(this._format('ERROR', message));
  }

  warning(message) {
    console.warn(this._format('WARNING', message));
  }

  debug(message) {
    if (process.env.DEBUG) {
      console.log(this._format('DEBUG', message));
    }
  }
}

// 智能服务识别器
class ServiceIdentifier {
  constructor(logger) {
    this.logger = logger;
  }

  // 通过多种方式识别服务
  async identifyService(pid, expectedType) {
    const identifiers = await Promise.all([
      this.checkCommandLine(pid),
      this.checkOpenFiles(pid),
      this.checkEnvironment(pid),
      this.checkWorkingDirectory(pid),
      this.checkListeningPorts(pid)
    ]);

    // 先检查是否在白名单中
    if (this.isProcessWhitelisted(identifiers)) {
      this.logger.info(`进程 ${pid} 在白名单中，跳过服务识别`);
      return 'whitelisted';
    }

    return this.analyzeIdentifiers(identifiers, expectedType);
  }

  // 检查命令行参数
  async checkCommandLine(pid) {
    try {
      const { stdout } = await execAsync(`ps -p ${pid} -o args=`);
      return { type: 'cmdline', value: stdout.trim() };
    } catch (e) {
      return { type: 'cmdline', value: null };
    }
  }

  // 检查打开的文件
  async checkOpenFiles(pid) {
    try {
      const { stdout } = await execAsync(`sudo lsof -p ${pid} 2>/dev/null | grep -E '(\.py|\.js|\.yaml|\.json)' | head -5`);
      const files = stdout.trim().split('\n').filter(line => line);
      return { type: 'files', value: files };
    } catch (e) {
      return { type: 'files', value: [] };
    }
  }

  // 检查环境变量
  async checkEnvironment(pid) {
    try {
      const { stdout } = await execAsync(`sudo cat /proc/${pid}/environ 2>/dev/null | tr '\\0' '\\n' | grep -E '(API|DOUYIN|SINGBOX|PATH)'`);
      const env = stdout.trim().split('\n').filter(line => line);
      return { type: 'env', value: env };
    } catch (e) {
      return { type: 'env', value: [] };
    }
  }

  // 检查工作目录
  async checkWorkingDirectory(pid) {
    try {
      const { stdout } = await execAsync(`sudo readlink /proc/${pid}/cwd 2>/dev/null`);
      return { type: 'cwd', value: stdout.trim() };
    } catch (e) {
      return { type: 'cwd', value: null };
    }
  }

  // 检查监听端口
  async checkListeningPorts(pid) {
    try {
      const { stdout } = await execAsync(`sudo ss -tlnp | grep "pid=${pid}" | awk '{print $4}' | cut -d: -f2`);
      const ports = stdout.trim().split('\n').filter(port => port).map(p => parseInt(p));
      return { type: 'ports', value: ports };
    } catch (e) {
      return { type: 'ports', value: [] };
    }
  }

  // 分析识别结果
  analyzeIdentifiers(identifiers, expectedType) {
    const cmdline = identifiers.find(i => i.type === 'cmdline')?.value || '';
    const files = identifiers.find(i => i.type === 'files')?.value || [];
    const env = identifiers.find(i => i.type === 'env')?.value || [];
    const cwd = identifiers.find(i => i.type === 'cwd')?.value || '';
    const ports = identifiers.find(i => i.type === 'ports')?.value || [];

    // 根据预期类型进行匹配
    switch (expectedType) {
      case 'douyin_api':
        return this.isDouyinApi(cmdline, files, cwd, ports);
      case 'singbox':
        return this.isSingbox(cmdline, files, env);
      case 'monitor':
        return this.isMonitor(cmdline, files);
      default:
        return false;
    }
  }

  // 检查进程是否在白名单中
  isProcessWhitelisted(identifiers) {
    const cwd = identifiers.find(i => i.type === 'cwd')?.value || '';
    const cmdline = identifiers.find(i => i.type === 'cmdline')?.value || '';
    const files = identifiers.find(i => i.type === 'files')?.value || [];
    
    // 检查工作目录是否在白名单路径下
    const whitelistPaths = this.config.processWhitelist?.paths || ['/home/<USER>/downloader'];
    for (const path of whitelistPaths) {
      if (cwd.startsWith(path)) {
        return true;
      }
      // 检查命令行或文件路径
      if (cmdline.includes(path)) {
        return true;
      }
      if (files.some(f => f.startsWith(path))) {
        return true;
      }
    }
    
    // 检查特定的进程名称白名单
    const whitelistProcesses = this.config.processWhitelist?.processes || [];
    for (const process of whitelistProcesses) {
      if (cmdline.includes(process)) {
        return true;
      }
    }
    
    return false;
  }

  // 判断是否为抖音API服务
  isDouyinApi(cmdline, files, cwd, ports) {
    const indicators = [
      cmdline.includes('start_multiport.py'),
      cmdline.includes('Douyin_TikTok_Download_API'),
      files.some(f => f.includes('Douyin_TikTok_Download_API')),
      cwd.includes('Douyin_TikTok_Download_API'),
      ports.some(p => p >= 8080 && p <= 8083)
    ];

    const matchCount = indicators.filter(i => i).length;
    return matchCount >= 2;
  }

  // 判断是否为Singbox服务
  isSingbox(cmdline, files, env) {
    const indicators = [
      cmdline.includes('sing-box'),
      cmdline.includes('config.json'),
      files.some(f => f.includes('sing-box') || f.includes('config.json')),
      env.some(e => e.includes('SINGBOX'))
    ];

    const matchCount = indicators.filter(i => i).length;
    return matchCount >= 2;
  }

  // 判断是否为监控服务
  isMonitor(cmdline, files) {
    const indicators = [
      cmdline.includes('integrated_monitor'),
      cmdline.includes('node') && cmdline.includes('monitor'),
      files.some(f => f.includes('integrated_monitor'))
    ];

    const matchCount = indicators.filter(i => i).length;
    return matchCount >= 1;
  }
}

// 智能端口管理器
class SmartPortManager {
  constructor(logger, config, serviceIdentifier) {
    this.logger = logger;
    this.config = config;
    this.serviceIdentifier = serviceIdentifier;
  }

  // 智能端口检测和清理
  async checkAndManagePort(port, expectedService) {
    this.logger.info(`检查端口 ${port} 的占用情况...`);

    try {
      // 获取占用端口的进程
      const { stdout } = await execAsync(`sudo lsof -i :${port} -t 2>/dev/null || echo ""`);
      const pids = stdout.trim().split('\n').filter(pid => pid);

      if (pids.length === 0) {
        this.logger.info(`端口 ${port} 未被占用`);
        return { available: true, action: 'none' };
      }

      // 分析每个占用端口的进程
      for (const pid of pids) {
        const serviceType = await this.serviceIdentifier.identifyService(pid, expectedService);

        if (serviceType === true || serviceType === expectedService) {
          this.logger.info(`端口 ${port} 被预期的服务占用 (PID: ${pid})`);
          return { available: false, action: 'keep', pid: pid };
        }

        // 检查是否在白名单中
        if (serviceType === 'whitelisted') {
          this.logger.info(`端口 ${port} 被白名单进程占用 (PID: ${pid})，跳过清理`);
          await sendMessage(
            `ℹ️ 端口占用提醒 - ${MACHINE_NAME}\n\n` +
            `时间: ${new Date().toLocaleString('zh-CN')}\n` +
            `端口: ${port}\n` +
            `进程PID: ${pid}\n` +
            `状态: 白名单进程，已跳过清理\n` +
            `说明: 该进程位于 /home/<USER>/downloader 目录下`,
            'info'
          );
          return { available: false, action: 'whitelisted', pid: pid };
        }

        // 非预期服务占用，准备清理
        this.logger.warning(`端口 ${port} 被非预期进程占用 (PID: ${pid})，准备清理...`);
        
        try {
          // 先尝试优雅停止
          await execAsync(`sudo kill -15 ${pid}`);
          await new Promise(resolve => setTimeout(resolve, 2000));

          // 检查是否已停止
          try {
            await execAsync(`ps -p ${pid}`);
            // 如果还在运行，强制停止
            await execAsync(`sudo kill -9 ${pid}`);
          } catch (e) {
            // 进程已停止
          }

          this.logger.info(`成功清理端口 ${port} 的占用进程`);
          // 发送消息通知
          await sendMessage(
            `⚠️ 清理端口占用 - ${MACHINE_NAME}\n\n` +
            `时间: ${new Date().toLocaleString('zh-CN')}\n` +
            `端口: ${port}\n` +
            `进程PID: ${pid}\n` +
            `操作: 已强制关闭非预期进程`,
            'warning'
          );
          return { available: true, action: 'cleaned', pid: pid };
        } catch (e) {
          this.logger.error(`清理端口 ${port} 失败: ${e.message}`);
          return { available: false, action: 'failed', pid: pid, error: e.message };
        }
      }
    } catch (e) {
      this.logger.error(`检查端口 ${port} 时出错: ${e.message}`);
      return { available: false, action: 'error', error: e.message };
    }
  }

  // 批量检查和管理端口
  async checkAndManageAllPorts() {
    const results = {
      api: [],
      proxy: [],
      clash: null,
      cleanedPorts: [],
      errors: []
    };

    // 检查API端口
    for (let i = 0; i < 4; i++) {
      const port = 8080 + i;
      const result = await this.checkAndManagePort(port, 'douyin_api');
      results.api.push({ port, ...result });
      if (result.action === 'cleaned') {
        results.cleanedPorts.push(`${port} (API实例${i})`);
      } else if (result.action === 'failed') {
        results.errors.push(`端口 ${port} 清理失败`);
      }
    }

    // 检查代理端口
    for (let i = 0; i < 4; i++) {
      const port = 1080 + i;
      const result = await this.checkAndManagePort(port, 'singbox');
      results.proxy.push({ port, ...result });
      if (result.action === 'cleaned') {
        results.cleanedPorts.push(`${port} (Singbox代理端口${i})`);
      } else if (result.action === 'failed') {
        results.errors.push(`端口 ${port} 清理失败`);
      }
    }

    // 检查Clash API端口
    const clashResult = await this.checkAndManagePort(9090, 'singbox');
    results.clash = { port: 9090, ...clashResult };
    if (clashResult.action === 'cleaned') {
      results.cleanedPorts.push(`9090 (Singbox Clash API)`);
    } else if (clashResult.action === 'failed') {
      results.errors.push(`端口 9090 清理失败`);
    }

    return results;
  }
}

const logger = new Logger();
const serviceIdentifier = new ServiceIdentifier(logger);

logger.info("启动集成监控服务（多实例版本V2 - JavaScript）...");
logger.info(`基础路径: ${BASE_PATH}`);

// 清理重复进程的函数
async function cleanupDuplicateProcesses() {
  logger.info("检查并清理重复的监控进程...");
  
  try {
    // 获取当前进程ID
    const currentPid = process.pid;
    
    // 查找所有运行中的监控进程，排除当前进程的子进程
    const { stdout } = await execAsync(`ps aux | grep -E 'node.*integrated_monitor_multi.js' | grep -v grep | grep -v "sudo\\|systemctl"`);
    
    if (stdout) {
      const lines = stdout.trim().split('\n');
      let killedCount = 0;
      let mainProcesses = [];
      
      // 首先识别主进程（不是由当前进程启动的）
      for (const line of lines) {
        const parts = line.split(/\s+/);
        const pid = parseInt(parts[1]);
        const ppid = parseInt(parts[2]); // 父进程ID
        
        // 检查进程启动时间，只清理早于当前进程的
        if (pid && pid !== currentPid) {
          // 获取进程的父进程ID
          try {
            const { stdout: ppidInfo } = await execAsync(`ps -p ${pid} -o ppid=`);
            const parentPid = parseInt(ppidInfo.trim());
            
            // 如果父进程不是当前进程，且不是systemd(1)，则认为是重复进程
            if (parentPid !== currentPid && parentPid !== 1) {
              mainProcesses.push(pid);
            }
          } catch (e) {
            // 进程可能已经消失
          }
        }
      }
      
      // 杀死识别出的重复进程
      for (const pid of mainProcesses) {
        try {
          await execAsync(`kill -9 ${pid}`);
          logger.info(`已杀死重复进程: PID ${pid}`);
          killedCount++;
        } catch (e) {
          logger.error(`无法杀死进程 ${pid}: ${e.message}`);
        }
      }
      
      if (killedCount > 0) {
        logger.info(`共清理了 ${killedCount} 个重复进程`);
        // 等待进程完全退出
        await new Promise(resolve => setTimeout(resolve, 2000));
      } else {
        logger.info("没有发现重复进程");
      }
    }
  } catch (e) {
    // grep没有找到匹配项时会返回错误，这是正常的
    if (!e.message.includes('Command failed')) {
      logger.error(`检查重复进程时出错: ${e.message}`);
    } else {
      logger.info("没有发现重复进程");
    }
  }
}

// 清理环境的函数
async function cleanupEnvironment() {
  logger.info("========================================");
  logger.info("开始环境清理和纠正...");
  logger.info("========================================");
  
  const cleanupReport = {
    stoppedServices: [],
    cleanedPorts: [],
    errors: []
  };
  
  let portResults = null;  // 保存端口检查结果用于报告
  
  try {
    // 1. 停止所有冗余的服务
    logger.info("第1步：停止冗余服务...");
    
    // 需要停止的冗余服务列表
    const redundantServices = [
      'Douyin_TikTok_API_Instance1.service',
      'Douyin_TikTok_API_Instance2.service', 
      'Douyin_TikTok_API_Instance3.service',
      'Douyin_TikTok_Download_API_New.service',
      'integrated-monitor-multi.service'  // Python版本的监控服务
    ];
    
    for (const service of redundantServices) {
      try {
        // 检查服务是否存在和状态
        const { stdout: statusOut } = await execAsync(`systemctl is-active ${service} 2>/dev/null || echo "inactive"`);
        const status = statusOut.trim();
        
        if (status !== 'inactive' && status !== 'failed') {
          logger.info(`停止冗余服务: ${service}`);
          await execAsync(`sudo systemctl stop ${service}`);
          await execAsync(`sudo systemctl disable ${service} 2>/dev/null || true`);
          cleanupReport.stoppedServices.push(service);
          
          // 发送关闭服务的消息
          await sendMessage(
            `🛑 关闭冗余服务 - ${MACHINE_NAME}\n\n` +
            `时间: ${new Date().toLocaleString('zh-CN')}\n` +
            `服务名称: ${service}\n` +
            `操作: 已停止并禁用服务`,
            'info'
          );
        }
      } catch (e) {
        logger.warning(`处理服务 ${service} 时出错: ${e.message}`);
      }
    }
    
    // 2. 清理端口占用（使用智能端口管理器）
    logger.info("第2步：智能清理端口占用...");
    
    // 创建智能端口管理器实例
    const portManager = new SmartPortManager(logger, config, serviceIdentifier);
    
    // 批量检查和管理所有端口
    portResults = await portManager.checkAndManageAllPorts();
    
    // 合并清理结果到报告
    cleanupReport.cleanedPorts.push(...portResults.cleanedPorts);
    cleanupReport.errors.push(...portResults.errors);
    
    // 输出端口状态摘要
    const cleanedCount = portResults.cleanedPorts.length;
    const errorCount = portResults.errors.length;
    if (cleanedCount > 0) {
      logger.info(`成功清理 ${cleanedCount} 个端口`);
    }
    if (errorCount > 0) {
      logger.warning(`${errorCount} 个端口清理失败`);
    }
    
    // 3. 清理遗留的Python进程
    logger.info("第3步：清理遗留进程...");
    
    // 获取进程白名单
    const processWhitelist = config.monitoring?.process_whitelist || [];
    logger.info(`进程白名单: ${processWhitelist.join(', ') || '无'}`);
    
    try {
      // 查找所有相关的Python进程（包括Douyin相关和其他Python进程）
      const { stdout } = await execAsync(`ps aux | grep -E "python.*(Douyin|surveillance)" | grep -v grep | grep -v "start_multiport.py" || echo ""`);
      
      if (stdout.trim()) {
        const lines = stdout.trim().split('\n');
        for (const line of lines) {
          const parts = line.split(/\s+/);
          const pid = parseInt(parts[1]);
          const cmd = parts.slice(10).join(' ');
          
          if (pid && !cmd.includes('start_multiport.py')) {
            // 检查是否在白名单中
            let isWhitelisted = false;
            for (const whitelistItem of processWhitelist) {
              if (cmd.includes(whitelistItem)) {
                isWhitelisted = true;
                logger.info(`跳过白名单进程: PID ${pid} - ${cmd.substring(0, 50)}...`);
                break;
              }
            }
            
            if (!isWhitelisted) {
              logger.info(`清理遗留Python进程: PID ${pid} - ${cmd.substring(0, 50)}...`);
              try {
                await execAsync(`sudo kill -9 ${pid}`);
                cleanupReport.cleanedPorts.push(`遗留进程 PID: ${pid}`);
              } catch (e) {
                logger.error(`无法清理进程 ${pid}: ${e.message}`);
              }
            }
          }
        }
      }
    } catch (e) {
      logger.error(`清理遗留进程时出错: ${e.message}`);
    }
    
    // 4. 验证必要的服务
    logger.info("第4步：验证必要服务...");
    
    const requiredServices = [
      'singbox.service',
      'Douyin_TikTok_API_Multiport.service',
      'integrated-monitor-multi-js.service'
    ];
    
    for (const service of requiredServices) {
      try {
        const { stdout } = await execAsync(`systemctl is-enabled ${service} 2>/dev/null || echo "disabled"`);
        if (stdout.trim() !== 'enabled') {
          logger.warning(`服务 ${service} 未启用，正在启用...`);
          await execAsync(`sudo systemctl enable ${service}`);
        }
      } catch (e) {
        logger.error(`检查服务 ${service} 时出错: ${e.message}`);
      }
    }
    
  } catch (e) {
    logger.error(`环境清理过程中出错: ${e.message}`);
    cleanupReport.errors.push(`总体错误: ${e.message}`);
  }
  
  // 生成增强的清理报告
  let reportMessage = `🧹 环境清理报告 - ${MACHINE_NAME}\n\n`;
  reportMessage += `时间: ${new Date().toLocaleString('zh-CN')}\n`;
  reportMessage += `基础路径: ${BASE_PATH}\n\n`;
  
  // 添加端口状态摘要
  if (portResults) {
    reportMessage += `🔌 端口状态:\n`;
    reportMessage += `  API端口 (8080-8083):\n`;
    portResults.api.forEach(r => {
      const status = r.action === 'keep' ? '✅ 正常' : 
                     r.action === 'cleaned' ? '🧹 已清理' : 
                     r.action === 'none' ? '✅ 未占用' : '⚠️ 异常';
      reportMessage += `    ${r.port}: ${status}\n`;
    });
    reportMessage += `  代理端口 (1080-1083):\n`;
    portResults.proxy.forEach(r => {
      const status = r.action === 'keep' ? '✅ 正常' : 
                     r.action === 'cleaned' ? '🧹 已清理' : 
                     r.action === 'none' ? '✅ 未占用' : '⚠️ 异常';
      reportMessage += `    ${r.port}: ${status}\n`;
    });
    if (portResults.clash) {
      const status = portResults.clash.action === 'keep' ? '✅ 正常' : 
                     portResults.clash.action === 'cleaned' ? '🧹 已清理' : 
                     portResults.clash.action === 'none' ? '✅ 未占用' : '⚠️ 异常';
      reportMessage += `  Clash API (9090): ${status}\n`;
    }
    reportMessage += `\n`;
  }
  
  if (cleanupReport.stoppedServices.length > 0) {
    reportMessage += `🛑 停止的冗余服务:\n`;
    cleanupReport.stoppedServices.forEach(s => reportMessage += `  - ${s}\n`);
    reportMessage += `\n`;
  }
  
  if (cleanupReport.cleanedPorts.length > 0) {
    reportMessage += `🧹 清理的进程:\n`;
    cleanupReport.cleanedPorts.forEach(p => reportMessage += `  - ${p}\n`);
    reportMessage += `\n`;
  }
  
  if (cleanupReport.errors.length > 0) {
    reportMessage += `⚠️ 遇到的错误:\n`;
    cleanupReport.errors.forEach(e => reportMessage += `  - ${e}\n`);
    reportMessage += `\n`;
  }
  
  if (cleanupReport.stoppedServices.length === 0 && 
      cleanupReport.cleanedPorts.length === 0 && 
      cleanupReport.errors.length === 0) {
    reportMessage += `✅ 环境状态正常，无需清理`;
  }
  
  logger.info("环境清理完成");
  return reportMessage;
}

// 立即执行清理
cleanupDuplicateProcesses().catch(e => {
  logger.error(`清理进程失败: ${e.message}`);
});

// 自动创建systemd服务文件
async function createSystemdService(serviceName, config) {
  const serviceFilePath = `/etc/systemd/system/${serviceName}`;
  
  try {
    // 检查服务文件是否存在
    const { stdout: checkResult } = await execAsync(`ls ${serviceFilePath} 2>/dev/null || echo "not_found"`);
    if (checkResult.trim() !== "not_found") {
      logger.info(`服务文件 ${serviceName} 已存在`);
      return true;
    }
    
    logger.info(`创建服务文件: ${serviceName}`);
    
    // 创建服务文件内容
    const serviceContent = config.content;
    
    // 写入临时文件
    const tempFile = `/tmp/${serviceName}`;
    fs.writeFileSync(tempFile, serviceContent);
    
    // 复制到系统目录（需要sudo权限）
    await execAsync(`sudo cp ${tempFile} ${serviceFilePath}`);
    await execAsync(`sudo chmod 644 ${serviceFilePath}`);
    
    // 删除临时文件
    fs.unlinkSync(tempFile);
    
    // 重新加载systemd
    await execAsync('sudo systemctl daemon-reload');
    
    logger.info(`服务文件 ${serviceName} 创建成功`);
    return true;
  } catch (e) {
    logger.error(`创建服务文件 ${serviceName} 失败: ${e.message}`);
    return false;
  }
}

// 获取Node.js可执行文件路径
async function getNodePath() {
  try {
    const { stdout: nodePath } = await execAsync('which node');
    return nodePath.trim();
  } catch (e) {
    // 如果which命令失败，尝试常见路径
    const commonPaths = [
      '/home/<USER>/.nvm/versions/node/v18.20.4/bin/node',
      '/usr/bin/node',
      '/usr/local/bin/node'
    ];
    
    for (const path of commonPaths) {
      try {
        await execAsync(`${path} --version`);
        return path;
      } catch (e) {
        continue;
      }
    }
    
    logger.error("无法找到Node.js可执行文件");
    return null;
  }
}

// 获取Python3可执行文件路径
async function getPython3Path() {
  try {
    const { stdout: pythonPath } = await execAsync('which python3');
    return pythonPath.trim();
  } catch (e) {
    return '/usr/bin/python3'; // 默认路径
  }
}

// 确保所有必要的服务存在
async function ensureServicesExist() {
  logger.info("检查并创建必要的systemd服务...");
  
  // 获取可执行文件路径
  const nodePath = await getNodePath();
  const pythonPath = await getPython3Path();
  
  if (!nodePath) {
    logger.error("无法创建服务：找不到Node.js");
    return [];
  }
  
  const services = [
    {
      name: 'singbox.service',
      content: `[Unit]
Description=Singbox Proxy Service
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=${BASE_PATH}/local-singbox
ExecStart=${BASE_PATH}/local-singbox/bin/sing-box run -c ${BASE_PATH}/local-singbox/config/config.json
Restart=on-failure
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target`
    },
    {
      name: 'Douyin_TikTok_API_Multiport.service',
      content: `[Unit]
Description=Douyin TikTok Download API Multi-Port Service
After=network.target singbox.service
Requires=singbox.service

[Service]
Type=simple
User=ubuntu
WorkingDirectory=${BASE_PATH}/Douyin_TikTok_Download_API
Environment="PATH=/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
Environment="PYTHONPATH=${BASE_PATH}/Douyin_TikTok_Download_API"
ExecStart=${pythonPath} ${BASE_PATH}/Douyin_TikTok_Download_API/start_multiport.py
Restart=on-failure
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target`
    },
    {
      name: 'integrated-monitor-multi-js.service',
      content: `[Unit]
Description=Integrated Monitor Service (Multi-Instance JavaScript Version)
After=network.target singbox.service Douyin_TikTok_API_Multiport.service
Wants=singbox.service Douyin_TikTok_API_Multiport.service

[Service]
Type=simple
User=ubuntu
WorkingDirectory=${BASE_PATH}/local-singbox/scripts
Environment="NODE_PATH=${nodePath.replace('/bin/node', '/lib/node_modules')}"
Environment="PATH=${nodePath.replace('/node', '')}:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
ExecStart=${nodePath} ${BASE_PATH}/local-singbox/scripts/integrated_monitor_multi.js
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target`
    }
  ];
  
  const results = [];
  for (const service of services) {
    const result = await createSystemdService(service.name, service);
    results.push({ name: service.name, success: result });
  }
  
  // 启用服务
  for (const result of results) {
    if (result.success) {
      try {
        await execAsync(`sudo systemctl enable ${result.name}`);
        logger.info(`已启用服务: ${result.name}`);
      } catch (e) {
        logger.error(`启用服务 ${result.name} 失败: ${e.message}`);
      }
    }
  }
  
  return results;
}

// 确保必要的配置文件存在
async function ensureConfigFilesExist() {
  logger.info("检查配置文件...");
  
  // 检查集成监控配置文件
  if (!fs.existsSync(CONFIG_FILE)) {
    logger.info("创建默认配置文件: integrated_config_multi.yaml");
    const defaultConfig = `# 集成服务配置文件 - 多实例版本
# 自动生成于: ${new Date().toISOString()}

# 代理服务配置
proxy:
  host: 127.0.0.1
  ports: [1080, 1081, 1082, 1083]
  port: 1080
  switch_interval: 1800
  node_config_file: ${BASE_PATH}/local-singbox/config/config.json

# API服务配置
api_instances:
  - name: "API实例0"
    systemd_service: "Douyin_TikTok_API_Multiport"
    host: 127.0.0.1
    port: 8080
    proxy_port: 1080
    proxy_group: "proxy0"
    config_path: ${BASE_PATH}/Douyin_TikTok_Download_API/crawlers/douyin/web/config.yaml
    test_endpoint: /api/douyin/web/handler_user_profile?sec_user_id=MS4wLjABAAAAp81HpCztQiGqFTajF3ey9x_s3CpNa-AAzhmr8oPXxvo
    
  - name: "API实例1"
    systemd_service: "Douyin_TikTok_API_Multiport"
    host: 127.0.0.1
    port: 8081
    proxy_port: 1081
    proxy_group: "proxy1"
    config_path: ${BASE_PATH}/Douyin_TikTok_Download_API/crawlers/douyin/web/config_instance1.yaml
    test_endpoint: /api/douyin/web/handler_user_profile?sec_user_id=MS4wLjABAAAAp81HpCztQiGqFTajF3ey9x_s3CpNa-AAzhmr8oPXxvo
    
  - name: "API实例2"
    systemd_service: "Douyin_TikTok_API_Multiport"
    host: 127.0.0.1
    port: 8082
    proxy_port: 1082
    proxy_group: "proxy2"
    config_path: ${BASE_PATH}/Douyin_TikTok_Download_API/crawlers/douyin/web/config_instance2.yaml
    test_endpoint: /api/douyin/web/handler_user_profile?sec_user_id=MS4wLjABAAAAp81HpCztQiGqFTajF3ey9x_s3CpNa-AAzhmr8oPXxvo
    
  - name: "API实例3"
    systemd_service: "Douyin_TikTok_API_Multiport"
    host: 127.0.0.1
    port: 8083
    proxy_port: 1083
    proxy_group: "proxy3"
    config_path: ${BASE_PATH}/Douyin_TikTok_Download_API/crawlers/douyin/web/config_instance3.yaml
    test_endpoint: /api/douyin/web/handler_user_profile?sec_user_id=MS4wLjABAAAAp81HpCztQiGqFTajF3ey9x_s3CpNa-AAzhmr8oPXxvo

# Cookie配置
cookie:
  pool_size: 20
  pool_file: ${BASE_PATH}/local-singbox/config/cookie_pool_multi.json
  tikhub_api_url: https://api.tikhub.io/api/v1/douyin/web/fetch_douyin_web_guest_cookie
  tikhub_api_key: kqNoW3xz9Ccnpwk8jzO4wHEP/hQ0osX2vZx44CW4sWB9rXoTWORd2z2UMg==
  user_agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
  update_cooldown: 3600
  max_retry_attempts: 5

# 监控配置
monitoring:
  error_threshold: 3
  error_window: 300
  check_interval: 30
  status_report_interval: 43200
  error_patterns:
    immediate_action:
      - "Resolved Douyin with cookies"
      - "搜索接口响应失败:"
      - "msToken=undefined"
      - "list is empty"
      - "odin_tt"
      - "UserProfileException"
      - "请求失败"
      - "post count is 0"
      - "_signature"
      - "失败结果: 用户"
    standard:
      - "ERROR"
      - "Exception"
      - "Failed"
      - "错误"
      - "失败"

# Supabase配置
supabase:
  url: https://wjanjmsywbydjbfrdkaz.supabase.co
  key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE
  does_table_id: 1

# 订阅设置
subscription:
  urls:
    - https://global.tagonline.asia/api/v1/client/subscribe?token=9fe0baab7e2b181e70e93b39aecdd3cb
    - https://global.tagonline.asia/api/v1/client/subscribe?token=b29cb7fa58ab93e36a2f06be4ac3e3cc
  update_interval: 24
  update_hour: 3
  last_used_url_file: ${BASE_PATH}/local-singbox/config/last_subscription_url.txt

# Clash API设置
clash_api:
  url: http://127.0.0.1:9090
  proxy_groups: [proxy0, proxy1, proxy2, proxy3]
  max_retries: 3
  max_latency: 1000
`;
    fs.writeFileSync(CONFIG_FILE, defaultConfig);
  }
  
  // 检查Cookie池文件
  const cookiePoolFile = `${BASE_PATH}/local-singbox/config/cookie_pool_multi.json`;
  if (!fs.existsSync(cookiePoolFile)) {
    logger.info("创建空的Cookie池文件");
    fs.writeFileSync(cookiePoolFile, JSON.stringify({ cookies: [] }, null, 2));
  }
}

// 在初始化时检查和创建服务
async function initializeEnvironment() {
  logger.info("初始化环境...");
  
  // 1. 创建必要的目录
  const requiredDirs = [
    `${BASE_PATH}/local-singbox/logs`,
    `${BASE_PATH}/local-singbox/config`,
    `${BASE_PATH}/local-singbox/scripts`,
    `${BASE_PATH}/local-singbox/bin`
  ];
  
  for (const dir of requiredDirs) {
    if (!fs.existsSync(dir)) {
      logger.info(`创建目录: ${dir}`);
      fs.mkdirSync(dir, { recursive: true });
    }
  }
  
  // 2. 确保配置文件存在
  await ensureConfigFilesExist();
  
  // 3. 确保服务文件存在
  await ensureServicesExist();
  
  // 4. 执行环境清理
  await cleanupEnvironment();
  
  logger.info("环境初始化完成");
}

//====================================================================
// 共享配置
//====================================================================

// 机器标识配置
let MACHINE_NAME = null;

// 定义已知机器的特征
const KNOWN_MACHINES = {
  "myserver": {
    "hostname": "instance-20231228-1715",
    "machine_id": "5bc46fed1c3e42278c5b97bcc0eb6566",
    "mac_address": "02:00:17:00:45:7c",
    "ip_pattern": "10.0.0."
  },
  "cript": {
    "hostname": "instance-20240803-0532",
    "ip_pattern": "10.0.0."
  }
};

// 获取机器名称的函数
async function getMachineName() {
  let hostname = "";
  let machineId = "";
  let macAddress = "";
  let ipAddress = "";
  
  try {
    hostname = os.hostname();
    logger.info(`检测到主机名: ${hostname}`);
  } catch (e) {
    logger.error(`获取主机名失败: ${e}`);
  }
  
  try {
    machineId = fs.readFileSync('/etc/machine-id', 'utf8').trim();
    logger.info(`检测到Machine ID: ${machineId}`);
  } catch (e) {
    logger.error(`获取Machine ID失败: ${e}`);
  }
  
  try {
    const { stdout } = await execAsync('ip link show enp0s6');
    const macMatch = stdout.match(/link\/ether\s+([0-9a-f:]{17})/);
    if (macMatch) {
      macAddress = macMatch[1];
      logger.info(`检测到MAC地址: ${macAddress}`);
    }
  } catch (e) {
    logger.error(`获取MAC地址失败: ${e}`);
  }
  
  try {
    const interfaces = os.networkInterfaces();
    for (const [name, addrs] of Object.entries(interfaces)) {
      for (const addr of addrs) {
        if (addr.family === 'IPv4' && !addr.internal) {
          ipAddress = addr.address;
          break;
        }
      }
      if (ipAddress) break;
    }
    logger.info(`检测到IP地址: ${ipAddress}`);
  } catch (e) {
    logger.error(`获取IP地址失败: ${e}`);
  }
  
  for (const [machineName, features] of Object.entries(KNOWN_MACHINES)) {
    let matchCount = 0;
    let totalFeatures = 0;
    
    if (features.hostname) {
      totalFeatures++;
      if (hostname && hostname === features.hostname) {
        matchCount++;
        logger.info(`主机名匹配 ${machineName}`);
      }
    }
    
    if (features.machine_id) {
      totalFeatures++;
      if (machineId && machineId === features.machine_id) {
        matchCount++;
        logger.info(`Machine ID匹配 ${machineName}`);
      }
    }
    
    if (features.mac_address) {
      totalFeatures++;
      if (macAddress && macAddress === features.mac_address) {
        matchCount++;
        logger.info(`MAC地址匹配 ${machineName}`);
      }
    }
    
    if (features.ip_pattern) {
      totalFeatures++;
      if (ipAddress && ipAddress.startsWith(features.ip_pattern)) {
        matchCount++;
        logger.info(`IP地址匹配 ${machineName}`);
      }
    }
    
    if (totalFeatures > 0 && matchCount / totalFeatures >= 0.5) {
      logger.info(`机器特征匹配率: ${matchCount}/${totalFeatures} = ${(matchCount/totalFeatures*100).toFixed(0)}%，识别为 ${machineName}`);
      return machineName;
    }
  }
  
  return hostname || "unknown-host";
}

// 从配置文件加载设置
const PROXY_HOST = config.proxy.host;
const PROXY_PORT = config.proxy.port;
const PROXY_PORTS = config.proxy.ports || [1080, 1081, 1082, 1083];

// Cookie池配置
const COOKIE_POOL_SIZE = config.cookie.pool_size;
const COOKIE_POOL_FILE = config.cookie.pool_file;
const TIKHUB_API_URL = config.cookie.tikhub_api_url;
const TIKHUB_API_KEY = config.cookie.tikhub_api_key;
const USER_AGENT = config.cookie.user_agent;
const COOKIE_UPDATE_COOLDOWN = config.cookie.update_cooldown;
const MAX_RETRY_ATTEMPTS = config.cookie.max_retry_attempts;

// 监控配置
const ERROR_THRESHOLD = config.monitoring.error_threshold;
const ERROR_WINDOW = config.monitoring.error_window;
const CHECK_INTERVAL = config.monitoring.check_interval;
const STATUS_REPORT_INTERVAL = config.monitoring.status_report_interval;
const IMMEDIATE_ACTION_PATTERNS = config.monitoring.error_patterns.immediate_action;
const STANDARD_ERROR_PATTERNS = config.monitoring.error_patterns.standard;

// Clash API配置
const CLASH_API_URL = config.clash_api?.url || 'http://127.0.0.1:9090';
const PROXY_GROUPS = config.clash_api?.proxy_groups || ['proxy0', 'proxy1', 'proxy2', 'proxy3'];
const MAX_RETRIES = config.clash_api?.max_retries || 3;
const MAX_LATENCY = config.clash_api?.max_latency || 1000;
const EXCELLENT_LATENCY = 200;
const GOOD_LATENCY = 500;
const SWITCH_INTERVAL = config.proxy.switch_interval;

// 订阅配置
const CONFIG_PATH = path.join(BASE_PATH, 'local-singbox/config/config.json');

// API实例配置
const api_instances = config.api_instances;

// IP信息API
const IP_INFO_API = "http://ip-api.com/json/{}?lang=zh-CN";

// Supabase配置
const SUPABASE_URL = config.supabase?.url || process.env.SUPABASE_URL;
const SUPABASE_KEY = config.supabase?.key || process.env.SUPABASE_KEY;
const DOES_TABLE_ID = config.supabase?.does_table_id || process.env.DOES_TABLE_ID || 1;

// 初始化Supabase客户端（配置自动重连）
let supabase = null;
if (SUPABASE_URL && SUPABASE_KEY) {
  supabase = createClient(SUPABASE_URL, SUPABASE_KEY, {
    realtime: {
      // 指数退避策略：1s, 2s, 4s, 8s, 16s, 最多30s
      reconnectAfterMs: (attempt) => {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 30000);
        logger.info(`🔄 Supabase Realtime 自动重连尝试 ${attempt}，延迟 ${delay}ms`);
        return delay;
      },
      // 增加其他配置以提高连接稳定性
      heartbeatIntervalMs: 30000,
      // 设置更长的超时时间
      timeout: 60000
    }
  });
  logger.info("✅ Supabase客户端已初始化（已启用自动重连）");
} else {
  logger.error("❌ 缺少Supabase配置，请设置SUPABASE_URL和SUPABASE_KEY");
}

//====================================================================
// 全局变量
//====================================================================

// Cookie池
let cookiePool = [];
let cookiePoolLastRefresh = null;
let cookieUpdateRunning = false;

// 节点使用计数器
const nodeUsageCounter = {};

// 代理IP缓存
const proxyIps = {};
const proxyLocations = {};

// 监控状态
const errorCounts = {};
const instanceMonitorIgnoreUntil = {};
const lastCheckedTimestamp = {}; // 记录每个实例上次检查的时间戳

// 线程控制标志
let nodeSwitchRunning = true;
let apiMonitorRunning = true;

// 订阅相关
let currentSubscriptionUrl = null; // 当前使用的订阅链接

// 上次的命令值，用于检测变化
let lastCommand = null;

// 已移除douyinlikes监控功能

// API统计数据
const apiStatistics = {
  // 每个端口的请求统计
  requests: {},
  // 每个端口的错误统计
  errors: {},
  // 每个端口的性能统计
  performance: {},
  // 流量统计
  traffic: {},
  // 统计开始时间
  startTime: new Date()
};

// 统计数据文件路径
const STATISTICS_FILE = path.join(BASE_PATH, 'local-singbox/logs/api_statistics.json');

//====================================================================
// Supabase消息发送函数
//====================================================================

async function sendMessage(message, messageType = 'info', metadata = {}) {
  if (!supabase) {
    logger.error("Supabase客户端未初始化，无法发送消息");
    return false;
  }

  try {
    const messageData = {
      message: `[${MACHINE_NAME}] ${message}`,
      timestamp: new Date().toISOString(),
      machine: MACHINE_NAME,
      type: messageType,
      metadata: metadata
    };

    const { data, error } = await supabase
      .from('does')
      .update({ message: JSON.stringify(messageData) })
      .eq('id', DOES_TABLE_ID);

    if (error) {
      logger.error(`更新Supabase消息失败: ${error.message}`);
      return false;
    }

    logger.info("消息已发送到Supabase");
    return true;
  } catch (e) {
    logger.error(`发送消息时出错: ${e}`);
    return false;
  }
}

//====================================================================
// 命令监听和处理
//====================================================================

// 启动时从数据库读取并更新订阅
async function updateSubscriptionFromDatabase() {
  if (!supabase) {
    logger.error("Supabase客户端未初始化，无法读取订阅URL");
    return false;
  }
  
  logger.info("从数据库读取订阅URL...");
  
  try {
    const { data, error } = await supabase
      .from('does')
      .select('command')
      .eq('id', DOES_TABLE_ID)
      .single();
      
    if (error) {
      logger.error(`读取数据库失败: ${error.message}`);
      return false;
    }
    
    if (data && data.command && isValidUrl(data.command)) {
      logger.info(`从数据库获取到订阅URL: ${data.command.substring(0, 80)}...`);
      logger.info("开始更新订阅节点...");
      
      const success = await updateSubscriptionWithUrl(data.command);
      
      if (success) {
        logger.info("✅ 启动时订阅更新成功");
        await sendMessage(
          `🚀 启动时订阅更新成功\n\n` +
          `时间: ${new Date().toLocaleString('zh-CN')}\n` +
          `机器: ${MACHINE_NAME}\n` +
          `订阅URL: ${data.command.substring(0, 80)}...\n` +
          `状态: 节点已更新`,
          'info'
        );
        return true;
      } else {
        logger.error("启动时订阅更新失败");
        return false;
      }
    } else {
      logger.info("数据库中没有有效的订阅URL");
      return false;
    }
  } catch (e) {
    logger.error(`启动时更新订阅失败: ${e.message}`);
    return false;
  }
}

// 全局变量存储 channel 实例和重连状态
let commandChannel = null;
let manualReconnectAttempt = 0;
let manualReconnectTimeout = null;
let isManuallyReconnecting = false;

// 检查Supabase连接健康状态
async function checkSupabaseConnection() {
  if (!supabase) {
    return false;
  }

  try {
    const { data, error } = await supabase
      .from('does')
      .select('id')
      .eq('id', DOES_TABLE_ID)
      .single();

    if (error) {
      logger.error(`Supabase连接检查失败: ${error.message}`);
      return false;
    }

    logger.info("✅ Supabase连接健康检查通过");
    return true;
  } catch (e) {
    logger.error(`Supabase连接检查异常: ${e.message}`);
    return false;
  }
}

async function setupCommandListener() {
  if (!supabase) {
    logger.error("❌ Supabase客户端未初始化，无法监听命令");
    return;
  }

  // 首先检查连接健康状态
  const isHealthy = await checkSupabaseConnection();
  if (!isHealthy) {
    logger.error("❌ Supabase连接不健康，将在稍后重试");
    // 延迟重试
    setTimeout(() => setupCommandListener(), 30000);
    return;
  }

  // 获取当前命令值
  try {
    const { data, error } = await supabase
      .from('does')
      .select('command')
      .eq('id', DOES_TABLE_ID)
      .single();

    if (!error && data) {
      if (data.command) {
        lastCommand = data.command;
        logger.info(`📋 初始命令值: ${lastCommand}`);

        // 根据初始命令值设置模式
        if (data.command === 'direct') {
          logger.info("🔄 启动时检测到direct命令，切换到直连模式");
          await switchToDirectMode();
        } else if (isValidUrl(data.command)) {
          logger.info("🔄 启动时检测到订阅URL，使用订阅模式");
          // 订阅更新已经在main函数中的updateSubscriptionFromDatabase处理了
        }
        // 其他命令在启动时不处理
      }
    }
  } catch (e) {
    logger.error(`❌ 获取初始值失败: ${e.message}`);
  }

  // 创建并订阅 channel
  subscribeToCommandChannel();
}

// 订阅 command channel 的函数
function subscribeToCommandChannel() {
  // 清理之前的手动重连计时器
  if (manualReconnectTimeout) {
    clearTimeout(manualReconnectTimeout);
    manualReconnectTimeout = null;
  }

  // 如果存在旧的 channel，先移除
  if (commandChannel) {
    logger.info("移除旧的 command channel");
    try {
      supabase.removeChannel(commandChannel);
    } catch (e) {
      logger.warning(`移除旧channel时出错: ${e.message}`);
    }
    commandChannel = null;
  }

  // 创建新的 channel
  const channelName = `command-changes-${Date.now()}`;
  commandChannel = supabase
    .channel(channelName)
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'does',
        filter: `id=eq.${DOES_TABLE_ID}`
      },
      async (payload) => {
        try {
          // 检查command字段变化
          const newCommand = payload.new.command;
          if (newCommand !== lastCommand) {
            logger.info(`✅ 检测到命令变化: ${lastCommand} -> ${newCommand}`);
            lastCommand = newCommand;
            await processCommand(newCommand);
          }
        } catch (e) {
          logger.error(`处理命令变化时出错: ${e.message}`);
        }
      }
    );

  // 监听 channel 状态变化
  commandChannel.on('system', {}, (payload) => {
    logger.info(`Channel 系统事件: ${JSON.stringify(payload)}`);
  });

  // 订阅并处理状态
  commandChannel.subscribe((status, err) => {
    logger.info(`Channel 状态: ${status}`);

    if (status === 'SUBSCRIBED') {
      logger.info("✅ 命令监听器已成功订阅");
      manualReconnectAttempt = 0; // 重置手动重连计数
      isManuallyReconnecting = false;
    } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT' || status === 'CLOSED') {
      logger.error(`❌ Channel 订阅失败，状态: ${status}，错误: ${err?.message || '未知'}`);

      // 只有在不是自动重连过程中才进行手动重连
      if (!isManuallyReconnecting) {
        scheduleManualReconnect();
      }
    }
  });
}

// 计划手动重连的函数
function scheduleManualReconnect() {
  // 防止重复调度
  if (isManuallyReconnecting) {
    return;
  }

  isManuallyReconnecting = true;
  manualReconnectAttempt++;

  // 计算重连延迟（指数退避）
  const delay = Math.min(1000 * Math.pow(2, manualReconnectAttempt - 1), 30000);

  logger.info(`🔄 计划手动重连 command channel，延迟 ${delay}ms (第 ${manualReconnectAttempt} 次尝试)`);

  // 清理之前的重连计时器
  if (manualReconnectTimeout) {
    clearTimeout(manualReconnectTimeout);
  }

  // 设置新的重连计时器
  manualReconnectTimeout = setTimeout(() => {
    logger.info(`🔄 开始手动重连 command channel...`);
    subscribeToCommandChannel();
  }, delay);
}

// 清理并退出的函数
function cleanupAndExit() {
  logger.info("开始清理资源...");

  // 停止线程
  nodeSwitchRunning = false;
  apiMonitorRunning = false;

  // 清理手动重连计时器
  if (manualReconnectTimeout) {
    clearTimeout(manualReconnectTimeout);
    manualReconnectTimeout = null;
  }

  // 清理 Supabase channel
  if (commandChannel) {
    logger.info("移除 command channel");
    try {
      supabase.removeChannel(commandChannel);
    } catch (e) {
      logger.warning(`清理channel时出错: ${e.message}`);
    }
    commandChannel = null;
  }

  logger.info("资源清理完成，程序退出");
  process.exit(0);
}

async function processCommand(command) {
  try {
    logger.info(`处理命令: ${command}`);

    // 检查是否是状态命令
    if (command === 'status') {
      await generateStatusReport();
      return;
    }

    // 检查是否是直连命令
    if (command === 'direct') {
      await switchToDirectMode();
      await updateDoesMessage(`✅ 已切换到直连模式 - ${new Date().toLocaleString('zh-CN')}`);
      return;
    }

    // 检查是否是有效的URL
    if (isValidUrl(command)) {
      logger.info("检测到订阅URL，开始更新订阅");
      const success = await updateSubscriptionWithUrl(command);
      
      if (success) {
        await updateDoesMessage(`✅ 订阅更新成功 - ${new Date().toLocaleString('zh-CN')}`);
        await sendMessage(
          `✅ 订阅更新成功\n\n` +
          `时间: ${new Date().toLocaleString('zh-CN')}\n` +
          `订阅URL: ${command.substring(0, 80)}...\n` +
          `状态: 更新成功`,
          'info'
        );
      } else {
        const errorMsg = `❌ 订阅更新失败 - ${new Date().toLocaleString('zh-CN')} - URL无效或解析失败`;
        await updateDoesMessage(errorMsg);
        await sendMessage(errorMsg, 'error');
      }
    } else if (command) {
      // 不是URL也不是status命令
      logger.warning(`未知命令或无效URL: ${command}`);
      await updateDoesMessage(`⚠️ 未知命令或无效URL - ${new Date().toLocaleString('zh-CN')}`);
    }
  } catch (e) {
    const errorMsg = `❌ 命令处理失败: ${e.message} - ${new Date().toLocaleString('zh-CN')}`;
    logger.error(errorMsg);
    await updateDoesMessage(errorMsg);
    await sendMessage(errorMsg, 'error');
  }
}

// 检查是否是有效的URL
function isValidUrl(string) {
  try {
    const url = new URL(string);
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch (_) {
    return false;
  }
}

// 更新does表的message字段
async function updateDoesMessage(message) {
  if (!supabase) {
    logger.error("Supabase客户端未初始化，无法更新message");
    return;
  }

  try {
    const { error } = await supabase
      .from('does')
      .update({ message: message })
      .eq('id', DOES_TABLE_ID);

    if (error) {
      logger.error(`更新message字段失败: ${error.message}`);
    } else {
      logger.info(`已更新message字段: ${message}`);
    }
  } catch (e) {
    logger.error(`更新message字段时出错: ${e}`);
  }
}

//====================================================================
// Cookie池管理
//====================================================================

function loadCookiePool() {
  try {
    if (fs.existsSync(COOKIE_POOL_FILE)) {
      const data = JSON.parse(fs.readFileSync(COOKIE_POOL_FILE, 'utf8'));
      cookiePool = data.cookies || [];
      logger.info(`从文件加载了 ${cookiePool.length} 个cookie`);
    }
  } catch (e) {
    logger.error(`加载cookie池失败: ${e}`);
    cookiePool = [];
  }
}

function saveCookiePool() {
  try {
    const data = {
      cookies: cookiePool,
      updated_at: new Date().toISOString()
    };
    fs.writeFileSync(COOKIE_POOL_FILE, JSON.stringify(data, null, 2));
    logger.info(`保存了 ${cookiePool.length} 个cookie到文件`);
  } catch (e) {
    logger.error(`保存cookie池失败: ${e}`);
  }
}

function addToCookiePool(cookie) {
  if (!cookiePool.includes(cookie)) {
    cookiePool.push(cookie);
    if (cookiePool.length > COOKIE_POOL_SIZE) {
      cookiePool.shift();
    }
    saveCookiePool();
  }
}

function getCookieFromPool() {
  if (cookiePool.length === 0) {
    logger.warning("Cookie池为空");
    return null;
  }
  
  const cookie = cookiePool.shift();
  cookiePool.push(cookie);
  saveCookiePool();
  return cookie;
}

async function fetchDouyinCookie() {
  try {
    logger.info(`正在从TikHub获取新的cookie...`);
    
    const response = await axios.get(
      TIKHUB_API_URL,
      {
        params: {
          user_agent: USER_AGENT
        },
        headers: {
          'Authorization': `Bearer ${TIKHUB_API_KEY}`,
          'Accept': 'application/json'
        },
        timeout: 30000
      }
    );
    
    if (response.data && response.data.code === 200 && response.data.data) {
      const cookie = response.data.data.Cookie;
      if (cookie) {
        logger.info("成功获取新cookie");
        return cookie;
      }
    }
    
    logger.error(`TikHub返回无效响应: ${JSON.stringify(response.data)}`);
    return null;
    
  } catch (error) {
    logger.error(`获取cookie失败: ${error.message}`);
    return null;
  }
}

async function refreshCookiePool() {
  logger.info("开始刷新cookie池...");
  let successCount = 0;
  let failCount = 0;
  
  while (cookiePool.length < COOKIE_POOL_SIZE && failCount < MAX_RETRY_ATTEMPTS) {
    const cookie = await fetchDouyinCookie();
    if (cookie) {
      addToCookiePool(cookie);
      successCount++;
      logger.info(`成功添加第 ${successCount} 个cookie，当前池大小: ${cookiePool.length}/${COOKIE_POOL_SIZE}`);
      
      await new Promise(resolve => setTimeout(resolve, 2000));
    } else {
      failCount++;
      logger.warning(`获取cookie失败 (${failCount}/${MAX_RETRY_ATTEMPTS})`);
      
      if (failCount < MAX_RETRY_ATTEMPTS) {
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
  }
  
  logger.info(`Cookie池刷新完成: 成功=${successCount}, 失败=${failCount}, 当前池大小=${cookiePool.length}`);
  return successCount > 0;
}

async function resetAndFillCookiePool() {
  logger.info("重置并填充cookie池...");
  cookiePool = [];
  saveCookiePool();
  
  const success = await refreshCookiePool();
  if (success) {
    await sendMessage(
      `🍪 Cookie池已重置并填充\n\n` +
      `Cookie数量: ${cookiePool.length}/${COOKIE_POOL_SIZE}\n` +
      `状态: 成功`,
      'info'
    );
  } else {
    await sendMessage(
      `⚠️ Cookie池填充失败\n\n` +
      `Cookie数量: ${cookiePool.length}/${COOKIE_POOL_SIZE}\n` +
      `请检查TikHub API`,
      'warning'
    );
  }
  
  return success;
}

//====================================================================
// 统计功能
//====================================================================

// 初始化实例统计数据
function initializeInstanceStatistics(instanceName) {
  if (!apiStatistics.requests[instanceName]) {
    apiStatistics.requests[instanceName] = {
      total: 0,
      success: 0,
      failed: 0,
      lastHour: [],
      lastDay: []
    };
  }
  
  if (!apiStatistics.errors[instanceName]) {
    apiStatistics.errors[instanceName] = {
      total: 0,
      byType: {},
      lastErrors: []
    };
  }
  
  if (!apiStatistics.performance[instanceName]) {
    apiStatistics.performance[instanceName] = {
      avgResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0,
      responseTimes: []
    };
  }
}

// 记录API请求
function recordApiRequest(instanceName, success, responseTime = null, errorType = null) {
  initializeInstanceStatistics(instanceName);
  
  const now = new Date();
  const stats = apiStatistics.requests[instanceName];
  
  // 更新总计数
  stats.total++;
  if (success) {
    stats.success++;
  } else {
    stats.failed++;
    
    // 记录错误
    if (errorType) {
      apiStatistics.errors[instanceName].total++;
      apiStatistics.errors[instanceName].byType[errorType] = 
        (apiStatistics.errors[instanceName].byType[errorType] || 0) + 1;
      
      // 保留最近的错误
      apiStatistics.errors[instanceName].lastErrors.push({
        time: now,
        type: errorType
      });
      
      // 只保留最近100个错误
      if (apiStatistics.errors[instanceName].lastErrors.length > 100) {
        apiStatistics.errors[instanceName].lastErrors.shift();
      }
    }
  }
  
  // 记录时间戳用于计算每小时/每天的统计
  stats.lastHour.push({ time: now, success });
  stats.lastDay.push({ time: now, success });
  
  // 清理超过1小时的记录
  const oneHourAgo = new Date(now - 3600000);
  stats.lastHour = stats.lastHour.filter(r => r.time > oneHourAgo);
  
  // 清理超过24小时的记录
  const oneDayAgo = new Date(now - 86400000);
  stats.lastDay = stats.lastDay.filter(r => r.time > oneDayAgo);
  
  // 记录响应时间
  if (responseTime !== null && success) {
    const perf = apiStatistics.performance[instanceName];
    perf.responseTimes.push(responseTime);
    
    // 只保留最近1000个响应时间
    if (perf.responseTimes.length > 1000) {
      perf.responseTimes.shift();
    }
    
    // 更新统计
    perf.minResponseTime = Math.min(perf.minResponseTime, responseTime);
    perf.maxResponseTime = Math.max(perf.maxResponseTime, responseTime);
    perf.avgResponseTime = perf.responseTimes.reduce((a, b) => a + b, 0) / perf.responseTimes.length;
  }
}

// 获取实例统计摘要
function getInstanceStatisticsSummary(instanceName) {
  // 从实例名称获取端口号
  const instance = api_instances.find(inst => inst.name === instanceName);
  const port = instance ? instance.port : null;
  
  if (!port || !apiStatistics.requests[port]) {
    // 如果没有统计数据，返回默认值
    return {
      totalRequests: 0,
      successRate: '0%',
      requestsPerHour: 0,
      successRateLastHour: '0%',
      totalErrors: 0,
      avgResponseTime: 'N/A',
      minResponseTime: 'N/A',
      maxResponseTime: 'N/A'
    };
  }
  
  const requests = apiStatistics.requests[port];
  const errors = apiStatistics.errors[port] || { total: 0 };
  const performance = apiStatistics.performance[port] || { 
    avgResponseTime: 0, 
    minResponseTime: Infinity, 
    maxResponseTime: 0 
  };
  
  // 计算成功率
  const successRate = requests.total > 0 ? (requests.success / requests.total * 100).toFixed(2) : 0;
  
  // 计算最近一小时的请求总数
  const requestsLastHour = requests.lastHour.reduce((sum, r) => sum + r.requests, 0);
  
  // 计算最近一小时的成功率
  const successCountLastHour = requests.lastHour.reduce((sum, r) => sum + r.success, 0);
  const totalCountLastHour = requests.lastHour.reduce((sum, r) => sum + r.requests, 0);
  const successRateLastHour = totalCountLastHour > 0 ? 
    (successCountLastHour / totalCountLastHour * 100).toFixed(2) : 0;
  
  return {
    totalRequests: requests.total,
    successRate: `${successRate}%`,
    requestsPerHour: requestsLastHour,
    successRateLastHour: `${successRateLastHour}%`,
    totalErrors: errors.total,
    avgResponseTime: performance.avgResponseTime > 0 ? performance.avgResponseTime.toFixed(2) + 'ms' : 'N/A',
    minResponseTime: performance.minResponseTime === Infinity ? 'N/A' : performance.minResponseTime + 'ms',
    maxResponseTime: performance.maxResponseTime > 0 ? performance.maxResponseTime + 'ms' : 'N/A'
  };
}

// 保存统计数据到文件
async function saveStatistics() {
  try {
    const data = {
      ...apiStatistics,
      savedAt: new Date().toISOString()
    };
    
    fs.writeFileSync(STATISTICS_FILE, JSON.stringify(data, null, 2));
    logger.debug("统计数据已保存");
  } catch (e) {
    logger.error(`保存统计数据失败: ${e}`);
  }
}

// 加载统计数据
function loadStatistics() {
  try {
    if (fs.existsSync(STATISTICS_FILE)) {
      const data = JSON.parse(fs.readFileSync(STATISTICS_FILE, 'utf8'));
      
      // 合并加载的数据
      Object.assign(apiStatistics, data);
      
      // 转换日期字符串回Date对象
      for (const instance in apiStatistics.requests) {
        const stats = apiStatistics.requests[instance];
        stats.lastHour = stats.lastHour.map(r => ({ ...r, time: new Date(r.time) }));
        stats.lastDay = stats.lastDay.map(r => ({ ...r, time: new Date(r.time) }));
      }
      
      logger.info("已加载历史统计数据");
    }
  } catch (e) {
    logger.error(`加载统计数据失败: ${e}`);
  }
}

//====================================================================
// API实例管理
//====================================================================

async function checkInstanceStatus(instance) {
  const startTime = Date.now();
  try {
    const url = `http://${instance.host}:${instance.port}${instance.test_endpoint}`;
    const response = await axios.get(url, { timeout: 10000 });
    const responseTime = Date.now() - startTime;
    
    // 记录成功的请求
    recordApiRequest(instance.name, true, responseTime);
    
    return response.status === 200;
  } catch (e) {
    // 记录失败的请求
    const errorType = e.code || 'UNKNOWN_ERROR';
    recordApiRequest(instance.name, false, null, errorType);
    
    return false;
  }
}

async function updateApiConfig(instance, newCookie) {
  try {
    const configPath = instance.config_path;
    const configData = yaml.load(fs.readFileSync(configPath, 'utf8'));
    
    // Fix: Update cookie in the correct path matching the YAML structure
    if (!configData.TokenManager || !configData.TokenManager.douyin || 
        !configData.TokenManager.douyin.headers) {
      logger.error(`${instance.name}: 配置文件结构不正确，缺少TokenManager.douyin.headers`);
      return false;
    }
    
    configData.TokenManager.douyin.headers.Cookie = newCookie;
    
    fs.writeFileSync(configPath, yaml.dump(configData));
    logger.info(`${instance.name}: 配置文件已更新`);
    return true;
  } catch (e) {
    logger.error(`${instance.name}: 更新配置失败 - ${e}`);
    return false;
  }
}

function getInstanceProxyInfo(instance) {
  const proxyPort = instance.proxy_port;
  const ip = proxyIps[proxyPort] || "未知";
  const location = proxyLocations[proxyPort] || "未知";
  return [proxyPort, ip, location];
}

//====================================================================
// 代理节点管理
//====================================================================

async function waitForApiReady(maxRetries = 30) {
  logger.info("等待Singbox Clash API就绪...");
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await axios.get(`${CLASH_API_URL}/version`, { timeout: 5000 });
      if (response.status === 200) {
        logger.info("Clash API已就绪");
        return true;
      }
    } catch (e) {
      logger.debug(`等待Clash API... (${i + 1}/${maxRetries})`);
    }
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  logger.error("Clash API未就绪");
  return false;
}

async function getAllProxies() {
  try {
    const response = await axios.get(`${CLASH_API_URL}/proxies`, { timeout: 10000 });
    return response.data;
  } catch (e) {
    logger.error(`获取代理信息失败: ${e.message}`);
    return null;
  }
}

async function getCurrentNode(group) {
  try {
    const proxiesInfo = await getAllProxies();
    if (proxiesInfo && proxiesInfo.proxies && proxiesInfo.proxies[group]) {
      return proxiesInfo.proxies[group].now || "未知";
    }
    return "未知";
  } catch (e) {
    logger.error(`获取当前节点失败: ${e.message}`);
    return "未知";
  }
}

async function switchNode(group, nodeName) {
  try {
    const response = await axios.put(
      `${CLASH_API_URL}/proxies/${group}`,
      { name: nodeName },
      { timeout: 10000 }
    );
    
    if (response.status === 204 || response.status === 200) {
      logger.info(`成功切换 ${group} 到 ${nodeName}`);
      return true;
    }
    return false;
  } catch (e) {
    logger.error(`切换节点失败: ${e.message}`);
    return false;
  }
}

// 切换到直连模式
async function switchToDirectMode() {
  logger.info("开始切换到直连模式...");
  
  try {
    // 并发切换所有代理组到直连
    const results = await Promise.allSettled(
      PROXY_GROUPS.map(group => switchNode(group, 'direct'))
    );
    
    // 检查切换结果
    const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length;
    const failedCount = results.length - successCount;
    
    if (successCount === results.length) {
      logger.info("✅ 所有代理组已成功切换到直连模式");
      
      // 获取IP信息以确认
      await getAllProxyIps();
      
      await sendMessage(
        `✅ 直连模式已启用 - ${MACHINE_NAME}\n\n` +
        `时间: ${new Date().toLocaleString('zh-CN')}\n` +
        `状态: 所有代理组已切换到直连模式\n` +
        `说明: 现在所有通过代理的请求都将直接连接，不再经过代理服务器\n\n` +
        `各端口状态:\n` +
        PROXY_PORTS.map((port, i) => {
          const ip = proxyIps[port] || "未知";
          const location = proxyLocations[port] || "未知";
          return `• 端口 ${port} (${PROXY_GROUPS[i]}): ${ip} - ${location}`;
        }).join('\n'),
        'info'
      );
      
      return true;
    } else {
      logger.error(`部分代理组切换失败: 成功 ${successCount}, 失败 ${failedCount}`);
      await sendMessage(
        `⚠️ 直连模式切换部分失败\n\n` +
        `成功: ${successCount}\n` +
        `失败: ${failedCount}\n` +
        `请检查服务状态`,
        'warning'
      );
      return false;
    }
  } catch (e) {
    logger.error(`切换到直连模式失败: ${e.message}`);
    await sendMessage(
      `❌ 切换到直连模式失败\n\n` +
      `错误: ${e.message}\n` +
      `时间: ${new Date().toLocaleString('zh-CN')}`,
      'error'
    );
    return false;
  }
}

async function testNodeLatency(nodeName) {
  try {
    const url = `${CLASH_API_URL}/proxies/${encodeURIComponent(nodeName)}/delay?timeout=5000&url=http://www.gstatic.com/generate_204`;
    const response = await axios.get(url, { timeout: 10000 });
    
    if (response.data && typeof response.data.delay === 'number') {
      return response.data.delay;
    }
    return -1;
  } catch (e) {
    logger.debug(`测试节点 ${nodeName} 延迟失败: ${e.message}`);
    return -1;
  }
}

async function getIpLocation(ip) {
  try {
    const response = await axios.get(
      IP_INFO_API.replace('{}', ip),
      { timeout: 10000 }
    );
    
    if (response.data && response.data.status === "success") {
      const country = response.data.country || "未知国家";
      const city = response.data.city || "未知城市";
      const isp = response.data.isp || "未知ISP";
      return `${country} ${city} (${isp})`;
    }
    return "未知";
  } catch (e) {
    logger.error(`获取IP地理位置信息失败: ${e.message}`);
    return "未知";
  }
}

async function getProxyIp(port) {
  try {
    const proxyUrl = `http://${PROXY_HOST}:${port}`;
    const response = await axios.get('http://httpbin.org/ip', {
      proxy: {
        host: PROXY_HOST,
        port: port
      },
      timeout: 10000
    });
    
    if (response.data && response.data.origin) {
      return response.data.origin.split(',')[0].trim();
    }
    return null;
  } catch (e) {
    logger.error(`获取端口 ${port} 的代理IP失败: ${e.message}`);
    return null;
  }
}

async function getAllProxyIps() {
  logger.info("正在获取所有代理IP信息...");
  
  const results = await Promise.allSettled(
    PROXY_PORTS.map(async (port) => {
      const ip = await getProxyIp(port);
      if (ip) {
        proxyIps[port] = ip;
        const location = await getIpLocation(ip);
        proxyLocations[port] = location;
        logger.info(`端口 ${port}: ${ip} (${location})`);
      }
      return { port, ip };
    })
  );
  
  return [proxyIps, proxyLocations];
}

function ensureDifferentNodes(selectedNodes, availableNodes) {
  const uniqueNodes = [...new Set(selectedNodes)];
  
  if (uniqueNodes.length === selectedNodes.length) {
    return selectedNodes;
  }
  
  const usedNodes = new Set();
  const result = [];
  
  for (let i = 0; i < selectedNodes.length; i++) {
    let node = selectedNodes[i];
    
    if (usedNodes.has(node)) {
      const alternatives = availableNodes.filter(n => !usedNodes.has(n));
      if (alternatives.length > 0) {
        node = alternatives[0];
      }
    }
    
    usedNodes.add(node);
    result.push(node);
  }
  
  return result;
}

function selectNextNodes(currentNodes, allNodes) {
  const shuffledNodes = [...allNodes].sort(() => Math.random() - 0.5);
  
  const testCount = Math.min(50, shuffledNodes.length);
  const latencies = {};
  logger.info(`开始测试 ${testCount} 个随机节点的延迟...`);
  
  // 并发测试延迟
  return Promise.all(
    shuffledNodes.slice(0, testCount).map(async (node) => {
      const latency = await testNodeLatency(node);
      if (latency > 0) {
        latencies[node] = latency;
      }
    })
  ).then(() => {
    logger.info(`延迟测试完成，获得 ${Object.keys(latencies).length} 个有效结果`);
    
    if (Object.keys(latencies).length === 0) {
      logger.error("没有可用的延迟测试结果");
      return currentNodes;
    }
    
    // 按质量分类节点
    const excellentNodes = {};
    const goodNodes = {};
    const acceptableNodes = {};
    const poorNodes = {};
    
    for (const [node, lat] of Object.entries(latencies)) {
      if (lat <= EXCELLENT_LATENCY) {
        excellentNodes[node] = lat;
      } else if (lat <= GOOD_LATENCY) {
        goodNodes[node] = lat;
      } else if (lat <= MAX_LATENCY) {
        acceptableNodes[node] = lat;
      } else {
        poorNodes[node] = lat;
      }
    }
    
    logger.info(`节点质量分布: 优秀(${Object.keys(excellentNodes).length}) 良好(${Object.keys(goodNodes).length}) 可接受(${Object.keys(acceptableNodes).length}) 较差(${Object.keys(poorNodes).length})`);
    
    // 过滤掉过高延迟的节点
    const filteredLatencies = { ...excellentNodes, ...goodNodes, ...acceptableNodes };
    
    if (Object.keys(filteredLatencies).length < PROXY_GROUPS.length) {
      logger.warning(`可用低延迟节点数(${Object.keys(filteredLatencies).length})少于代理组数(${PROXY_GROUPS.length})`);
      if (Object.keys(filteredLatencies).length === 0) {
        const sortedByLatency = Object.entries(latencies).sort((a, b) => a[1] - b[1]);
        Object.assign(filteredLatencies, Object.fromEntries(sortedByLatency.slice(0, PROXY_GROUPS.length)));
        logger.warning(`使用延迟最低的 ${Object.keys(filteredLatencies).length} 个节点`);
      }
    }
    
    // 计算节点评分
    function calculateNodeScore(node, latency) {
      const latencyScore = Math.max(0, (MAX_LATENCY - latency) / MAX_LATENCY);
      const usageCount = nodeUsageCounter[node] || 0;
      const usagePenalty = Math.min(usageCount * 0.05, 0.5);
      return latencyScore * (1 - usagePenalty);
    }
    
    // 公平分配节点给各个代理组
    const selectedNodes = new Array(PROXY_GROUPS.length).fill(null);
    let assignedCount = 0;
    
    // 优先分配优质节点
    for (const [categoryName, categoryNodes] of [
      ["excellent", excellentNodes],
      ["good", goodNodes],
      ["acceptable", acceptableNodes]
    ]) {
      if (assignedCount >= PROXY_GROUPS.length || Object.keys(categoryNodes).length === 0) {
        continue;
      }
      
      const sortedCategory = Object.entries(categoryNodes).sort(
        (a, b) => calculateNodeScore(b[0], b[1]) - calculateNodeScore(a[0], a[1])
      );
      
      for (const [node, latency] of sortedCategory) {
        if (assignedCount >= PROXY_GROUPS.length) {
          break;
        }
        
        let groupIndex = assignedCount % PROXY_GROUPS.length;
        while (selectedNodes[groupIndex] !== null) {
          groupIndex = (groupIndex + 1) % PROXY_GROUPS.length;
        }
        
        selectedNodes[groupIndex] = node;
        assignedCount++;
        
        const score = calculateNodeScore(node, latency);
        logger.info(`分配 ${categoryName} 节点 ${node} 到 ${PROXY_GROUPS[groupIndex]}: 延迟=${latency}ms, 评分=${score.toFixed(3)}`);
      }
    }
    
    // 填充剩余未分配的位置
    for (let i = 0; i < PROXY_GROUPS.length; i++) {
      if (selectedNodes[i] === null) {
        const availableNodes = Object.keys(filteredLatencies).filter(node => !selectedNodes.includes(node));
        if (availableNodes.length > 0) {
          const bestNode = availableNodes.reduce((best, node) => {
            const bestScore = calculateNodeScore(best, filteredLatencies[best]);
            const nodeScore = calculateNodeScore(node, filteredLatencies[node]);
            return nodeScore > bestScore ? node : best;
          });
          selectedNodes[i] = bestNode;
          logger.warning(`填充位置 ${i} 使用节点 ${bestNode}`);
        }
      }
    }
    
    // 确保所有节点都不相同
    const finalNodes = ensureDifferentNodes(selectedNodes, Object.keys(filteredLatencies));
    
    // 更新使用计数并记录结果
    logger.info("=== 最终节点分配结果 ===");
    for (let i = 0; i < PROXY_GROUPS.length; i++) {
      const group = PROXY_GROUPS[i];
      const node = finalNodes[i];
      if (node) {
        nodeUsageCounter[node] = (nodeUsageCounter[node] || 0) + 1;
        const latency = filteredLatencies[node] || latencies[node] || "未知";
        const usageCount = nodeUsageCounter[node];
        if (typeof latency === 'number') {
          const score = calculateNodeScore(node, latency);
          logger.info(`${group} -> ${node}: 延迟=${latency}ms, 评分=${score.toFixed(3)}, 使用次数=${usageCount}`);
        } else {
          logger.info(`${group} -> ${node}: 延迟=${latency}, 使用次数=${usageCount}`);
        }
      } else {
        logger.error(`${group} -> 未分配节点!`);
      }
    }
    logger.info("========================");
    
    return finalNodes;
  });
}

async function switchAllNodes() {
  logger.info("开始切换所有代理组的节点...");
  
  const proxiesInfo = await getAllProxies();
  if (!proxiesInfo || !proxiesInfo.proxies) {
    logger.error("无法获取代理信息");
    return false;
  }
  
  const groupInfo = proxiesInfo.proxies[PROXY_GROUPS[0]] || {};
  const allNodes = groupInfo.all || [];
  
  if (allNodes.length === 0) {
    logger.error("没有可用节点");
    return false;
  }
  
  // 获取当前节点
  const currentNodes = {};
  for (const group of PROXY_GROUPS) {
    currentNodes[group] = await getCurrentNode(group);
  }
  
  // 选择新节点
  const newNodes = await selectNextNodes(Object.values(currentNodes), allNodes);
  
  // 同时切换所有节点
  const switchResults = {};
  const switchPromises = PROXY_GROUPS.map(async (group, index) => {
    const node = newNodes[index];
    try {
      const success = await switchNode(group, node);
      switchResults[group] = [node, success];
    } catch (e) {
      logger.error(`切换 ${group} 到 ${node} 时出错: ${e}`);
      switchResults[group] = [node, false];
    }
  });
  
  await Promise.all(switchPromises);
  
  // 等待切换生效
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 获取新的IP信息
  const oldIps = { ...proxyIps };
  await getAllProxyIps();
  
  // 构建切换报告
  let switchMessage = `🔄 代理节点批量切换 - ${MACHINE_NAME}\n\n`;
  switchMessage += `时间: ${new Date().toLocaleString('zh-CN')}\n`;
  switchMessage += `机器: ${MACHINE_NAME}\n\n`;
  switchMessage += `切换结果:\n`;
  
  for (let i = 0; i < PROXY_GROUPS.length; i++) {
    const group = PROXY_GROUPS[i];
    const [node, success] = switchResults[group] || ["未知", false];
    const port = PROXY_PORTS[i];
    const oldIp = oldIps[port] || "未知";
    const newIp = proxyIps[port] || "未知";
    const newLocation = proxyLocations[port] || "未知";
    
    const status = success ? "✅" : "❌";
    switchMessage += `\n${status} ${group}: ${currentNodes[group]} → ${node}\n`;
    switchMessage += `  端口: ${port}\n`;
    switchMessage += `  IP: ${oldIp} → ${newIp} (${newLocation})\n`;
  }
  
  // 统计信息
  const totalSwitches = Object.values(nodeUsageCounter).reduce((a, b) => a + b, 0);
  switchMessage += `\n统计信息:\n`;
  switchMessage += `• 总切换次数: ${totalSwitches}\n`;
  switchMessage += `• 已使用节点数: ${Object.keys(nodeUsageCounter).length}`;
  
  await sendMessage(switchMessage, 'info');
  
  return Object.values(switchResults).every(([_, success]) => success);
}

async function selectBestNodesForGroups() {
  logger.info("开始智能节点质量检测和优化...");
  
  const proxiesInfo = await getAllProxies();
  if (!proxiesInfo || !proxiesInfo.proxies) {
    logger.error("无法获取代理信息");
    return false;
  }
  
  const groupInfo = proxiesInfo.proxies[PROXY_GROUPS[0]] || {};
  const allNodes = groupInfo.all || [];
  
  if (allNodes.length === 0) {
    logger.error("没有可用节点");
    return false;
  }
  
  // 获取当前节点
  const currentNodes = await Promise.all(
    PROXY_GROUPS.map(group => getCurrentNode(group))
  );
  
  // 选择最优节点
  const bestNodes = await selectNextNodes(currentNodes, allNodes);
  
  // 切换到最优节点
  const switchPromises = PROXY_GROUPS.map(async (group, index) => {
    const newNode = bestNodes[index];
    const currentNode = currentNodes[index];
    
    if (newNode && newNode !== currentNode) {
      return switchNode(group, newNode);
    }
    return true;
  });
  
  const results = await Promise.all(switchPromises);
  
  return results.every(result => result === true);
}

//====================================================================
// 订阅更新功能
//====================================================================

function parseSubscriptionContent(content) {
  try {
    // 尝试base64解码
    const decoded = Buffer.from(content, 'base64').toString('utf-8');
    const nodes = [];
    
    // 解析每一行
    for (const line of decoded.split('\n')) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;
      
      // 解析shadowsocks链接
      if (trimmedLine.startsWith('ss://')) {
        try {
          const nodeInfo = parseSsUrl(trimmedLine);
          if (nodeInfo) {
            nodes.push(nodeInfo);
          }
        } catch (e) {
          logger.debug(`解析SS节点失败: ${trimmedLine}, 错误: ${e}`);
        }
      }
      // 可以添加其他协议的解析
      else if (trimmedLine.startsWith('vmess://')) {
        // TODO: 添加vmess解析
      }
    }
    
    logger.info(`成功解析 ${nodes.length} 个节点`);
    return nodes;
  } catch (e) {
    logger.error(`解析订阅内容失败: ${e}`);
    return [];
  }
}

function parseSsUrl(ssUrl) {
  try {
    // 移除 ss:// 前缀
    const ssData = ssUrl.substring(5);
    
    // 分离节点信息和参数
    let remark = null;
    let mainData = ssData;
    
    if (ssData.includes('#')) {
      const parts = ssData.split('#', 2);
      mainData = parts[0];
      remark = decodeURIComponent(parts[1]);
    }
    
    // 分离主体和参数
    let mainPart = mainData;
    let params = '';
    
    if (mainData.includes('?')) {
      const parts = mainData.split('?', 2);
      mainPart = parts[0];
      params = parts[1];
    }
    
    // 解析主体部分
    let method, password, server, port;
    
    if (mainPart.includes('@')) {
      // 格式: base64(method:password)@server:port
      const [authB64, serverInfo] = mainPart.split('@', 2);
      const authB64Padded = authB64 + '='.repeat((4 - authB64.length % 4) % 4);
      const auth = Buffer.from(authB64Padded, 'base64').toString('utf-8');
      [method, password] = auth.split(':', 2);
      
      // 移除端口号后的斜杠
      const cleanServerInfo = serverInfo.split('/')[0];
      [server, port] = cleanServerInfo.split(':', 2);
    } else {
      // 格式: base64(method:password@server:port)
      const decoded = Buffer.from(mainPart, 'base64').toString('utf-8');
      const match = decoded.match(/(.+?):(.+?)@(.+?):(\d+)/);
      if (match) {
        [, method, password, server, port] = match;
      } else {
        return null;
      }
    }
    
    // 解析参数
    let plugin = null;
    let pluginOpts = null;
    
    if (params) {
      const searchParams = new URLSearchParams(params);
      const pluginParam = searchParams.get('plugin');
      if (pluginParam) {
        const pluginParts = pluginParam.split(';');
        plugin = pluginParts[0];
        if (pluginParts.length > 1) {
          pluginOpts = pluginParts.slice(1).join(';');
        }
      }
    }
    
    const nodeInfo = {
      type: 'shadowsocks',
      tag: remark || `${server}:${port}`,
      server: server,
      server_port: parseInt(port),
      method: method,
      password: password
    };
    
    if (plugin) {
      // 映射插件名称
      if (plugin === 'simple-obfs') {
        plugin = 'obfs-local';
      }
      nodeInfo.plugin = plugin;
      if (pluginOpts) {
        nodeInfo.plugin_opts = pluginOpts;
      }
    }
    
    return nodeInfo;
  } catch (e) {
    logger.debug(`解析SS URL失败: ${e}`);
    return null;
  }
}

async function generateSingBoxConfig(nodes) {
  try {
    // 读取现有配置
    const config = JSON.parse(fs.readFileSync(CONFIG_PATH, 'utf8'));
    
    // 更新outbounds
    const newOutbounds = [];
    
    // 保留selector和urltest
    for (const outbound of config.outbounds || []) {
      if (['selector', 'urltest', 'direct', 'block', 'dns'].includes(outbound.type)) {
        // 如果是selector，更新其outbounds列表
        if (outbound.type === 'selector') {
          const tag = outbound.tag || '';
          if (tag.startsWith('proxy') || tag === 'proxy') {
            // 添加direct选项和所有节点
            outbound.outbounds = ['direct', ...nodes.map((_, i) => `server${i + 1}`)];
            // 确保default指向存在的节点
            if (outbound.default && !['direct', ...nodes.map((_, i) => `server${i + 1}`)].includes(outbound.default)) {
              outbound.default = 'server1';
            }
          }
        }
        // 如果是urltest (auto)，也需要更新其outbounds列表
        else if (outbound.type === 'urltest') {
          // urltest不包含direct，因为direct无法进行延迟测试
          outbound.outbounds = nodes.map((_, i) => `server${i + 1}`);
        }
        newOutbounds.push(outbound);
      }
    }
    
    // 添加新节点
    nodes.forEach((node, i) => {
      node.tag = `server${i + 1}`;
      newOutbounds.push(node);
    });
    
    config.outbounds = newOutbounds;
    
    // 写入新配置
    fs.writeFileSync(CONFIG_PATH, JSON.stringify(config, null, 2));
    
    logger.info(`成功更新配置，共 ${nodes.length} 个节点`);
    return true;
  } catch (e) {
    logger.error(`生成sing-box配置失败: ${e}`);
    return false;
  }
}

// 使用指定URL更新订阅
async function updateSubscriptionWithUrl(url) {
  if (!url) {
    logger.warning("没有提供URL");
    return false;
  }
  
  try {
    currentSubscriptionUrl = url; // 保存当前使用的订阅链接
    
    logger.info(`开始更新订阅`);
    logger.info(`订阅URL: ${url.substring(0, 50)}...`);
    
    // 获取订阅内容
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 30000
    });
    
    if (response.status !== 200) {
      logger.error(`获取订阅失败，状态码: ${response.status}`);
      return false;
    }
    
    // 解析订阅内容
    const nodes = parseSubscriptionContent(response.data);
    if (!nodes || nodes.length === 0) {
      logger.error("订阅内容解析失败或没有有效节点");
      return false;
    }
    
    // 生成新配置
    if (!await generateSingBoxConfig(nodes)) {
      logger.error("生成配置失败");
      return false;
    }
    
    // 重置节点使用计数器
    Object.keys(nodeUsageCounter).forEach(key => delete nodeUsageCounter[key]);
    logger.info("已重置节点使用计数器");
    
    // 重启sing-box服务
    logger.info("正在重启sing-box服务...");
    const { stderr } = await execAsync("sudo systemctl restart singbox.service");
    
    if (stderr && !stderr.includes('Warning')) {
      logger.error(`sing-box服务重启失败: ${stderr}`);
      return false;
    }
    
    logger.info("sing-box服务重启成功");
    
    // 等待服务就绪
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // 触发一次节点切换以使用新节点
    await switchAllNodes();
    
    logger.info(`订阅更新完成，共获取 ${nodes.length} 个节点`);
    
    return true;
  } catch (e) {
    logger.error(`更新订阅时发生错误: ${e}`);
    return false;
  }
}

//====================================================================
// API监控功能
//====================================================================

// 访问统计数据
const accessStats = {
  8080: { lastMinute: 0, success: 0, errors: 0 },
  8081: { lastMinute: 0, success: 0, errors: 0 },
  8082: { lastMinute: 0, success: 0, errors: 0 },
  8083: { lastMinute: 0, success: 0, errors: 0 }
};

async function reportApiStatus() {
  try {
    const now = new Date();
    const statusList = [];
    let totalAccess = 0;
    
    logger.debug("开始收集API访问统计...");
    
    // 收集访问统计
    for (const instance of api_instances) {
      const port = instance.port;
      const stats = await collectPortAccessStats(instance);
      accessStats[port] = stats;
      totalAccess += stats.lastMinute;
      logger.debug(`端口 ${port} 访问统计: ${stats.lastMinute} 次请求`);
    }
  
  // 更新apiStatistics中的统计数据
  for (const instance of api_instances) {
    const port = instance.port;
    const stats = accessStats[port];
    
    // 初始化端口统计（如果不存在）
    if (!apiStatistics.requests[port]) {
      apiStatistics.requests[port] = { 
        total: 0, 
        success: 0, 
        errors: 0, 
        lastHour: [], 
        lastDay: [] 
      };
    }
    
    // 更新总计数
    apiStatistics.requests[port].total += stats.lastMinute;
    apiStatistics.requests[port].success += stats.success;
    apiStatistics.requests[port].errors += stats.errors;
    
    // 记录时间序列数据
    const now = new Date();
    const record = {
      time: now,
      requests: stats.lastMinute,
      success: stats.success,
      errors: stats.errors
    };
    
    // 添加到最近一小时记录
    apiStatistics.requests[port].lastHour.push(record);
    // 只保留最近60条记录（60分钟）
    if (apiStatistics.requests[port].lastHour.length > 60) {
      apiStatistics.requests[port].lastHour.shift();
    }
    
    // 添加到最近一天记录（每小时一条）
    const lastDayRecord = apiStatistics.requests[port].lastDay;
    if (lastDayRecord.length === 0 || 
        now.getTime() - new Date(lastDayRecord[lastDayRecord.length - 1].time).getTime() >= 3600000) {
      apiStatistics.requests[port].lastDay.push(record);
      // 只保留最近24条记录（24小时）
      if (apiStatistics.requests[port].lastDay.length > 24) {
        apiStatistics.requests[port].lastDay.shift();
      }
    }
  }
  
  // 保存统计数据
  await saveStatistics();
  
  logger.debug(`总访问次数: ${totalAccess}`);
  
  // 如果有访问，生成并发送报告
  if (totalAccess > 0) {
    let accessReport = `📊 API访问统计 (最近1分钟)\n\n`;
    
    for (const instance of api_instances) {
      const port = instance.port;
      const stats = accessStats[port];
      const status = await checkInstanceStatus(instance);
      
      if (stats.lastMinute > 0) {
        const successRate = stats.success > 0 ? ((stats.success / stats.lastMinute) * 100).toFixed(1) : 0;
        const statusIcon = successRate < 50 ? "❌" : "✅";
        accessReport += `端口 ${port}: ${stats.lastMinute} 次请求 (成功率: ${successRate}%) ${statusIcon}\n`;
      } else {
        accessReport += `端口 ${port}: 无请求\n`;
      }
    }
    
    accessReport += `\n总计: ${totalAccess} 次请求`;
    
    // 发送消息
    await sendMessage(accessReport, 'stats');
    logger.info(`访问统计: 总计 ${totalAccess} 次请求`);
  } else {
    // 仍然记录状态，但不发送消息
    for (const instance of api_instances) {
      const status = await checkInstanceStatus(instance);
      const [proxyPort, ip, location] = getInstanceProxyInfo(instance);
      const statusIcon = status ? "✅" : "❌";
      statusList.push(`${instance.name} (端口${instance.port}): ${statusIcon}`);
    }
    logger.info(`API状态: ${statusList.join(', ')} - 无访问请求`);
  }
  } catch (e) {
    logger.error(`报告API状态时出错: ${e.message}`);
  }
}

// 收集端口访问统计
async function collectPortAccessStats(instance) {
  const stats = { lastMinute: 0, success: 0, errors: 0 };
  
  try {
    // 使用端口标识统计访问量（不依赖PID）
    const { stdout: portLogs } = await execAsync(
      `sudo journalctl -u ${instance.systemd_service} --since "1 minute ago" --no-pager | grep "PORT:${instance.port}" || echo ""`
    );
    
    if (portLogs) {
      const lines = portLogs.trim().split('\n').filter(line => line);
      
      for (const line of lines) {
        // 匹配新的端口标识日志格式
        // 格式: PORT:8080 - INFO - 127.0.0.1:xxxxx - "GET /api/... HTTP/1.1" 200
        const requestMatch = line.match(/PORT:(\d+)\s*-\s*INFO\s*-\s*[^-]+\s*-\s*"([^"]*HTTP\/1\.1)"\s*(\d{3})/);
        if (requestMatch) {
          const port = parseInt(requestMatch[1]);
          const statusCode = parseInt(requestMatch[3]);
          
          // 确保是当前端口的请求
          if (port === instance.port) {
            stats.lastMinute++;
            
            if (statusCode >= 200 && statusCode < 300) {
              stats.success++;
            } else if (statusCode >= 400) {
              stats.errors++;
            } else {
              stats.success++; // 其他状态码默认算成功
            }
          }
        }
      }
    }
    
    // 调试信息
    logger.info(`端口 ${instance.port}: 统计到 ${stats.lastMinute} 次访问 (成功: ${stats.success}, 错误: ${stats.errors})`);
    if (stats.lastMinute === 0 && portLogs) {
      logger.warning(`端口 ${instance.port}: 有日志但未匹配到访问记录，日志样例: ${portLogs.split('\n')[0] || '无'}`);
    }
    
    
  } catch (e) {
    logger.error(`收集端口 ${instance.port} 访问统计失败: ${e.message}`);
  }
  
  // 记录统计结果
  if (stats.lastMinute > 0 || process.env.DEBUG) {
    logger.debug(`端口 ${instance.port} 统计结果: 总请求=${stats.lastMinute}, 成功=${stats.success}, 错误=${stats.errors}`);
  }
  
  return stats;
}

async function checkApiErrors(instance) {
  try {
    // 使用相对时间查询，避免时区问题
    const { stdout } = await execAsync(
      `sudo journalctl -u ${instance.systemd_service} --since "1 minute ago" --no-pager`
    );
    
    const lines = stdout.split('\n');
    const errors = [];
    const uniqueErrors = new Set(); // 用于去重
    
    // 获取当前实例的端口号
    const instancePort = instance.port;
    
    for (const line of lines) {
      // 跳过空行
      if (!line.trim()) continue;
      
      // 检查日志是否包含端口标识（格式：PORT:8080 或 port:8080）
      const portMatch = line.match(/PORT:(\d+)|port:(\d+)/i);
      const logPort = portMatch ? (portMatch[1] || portMatch[2]) : null;
      
      // 如果日志中有端口信息，但不是当前实例的端口，跳过
      if (logPort && parseInt(logPort) !== instancePort) {
        continue;
      }
      
      // 如果是多端口服务，且日志中没有端口信息，直接跳过
      // 只有明确标记了端口的错误才处理
      const isMultiPortService = api_instances.filter(inst => inst.systemd_service === instance.systemd_service).length > 1;
      if (isMultiPortService && !logPort) {
        continue;  // 跳过所有没有端口信息的错误
      }
      
      // 提取时间戳（如果存在）来进一步过滤
      const timestampMatch = line.match(/^(\w+\s+\d+\s+\d+:\d+:\d+)/);
      
      // 检查需要立即行动的错误
      for (const pattern of IMMEDIATE_ACTION_PATTERNS) {
        if (line.includes(pattern)) {
          // 只处理有端口信息且端口匹配当前实例的错误
          if (logPort && parseInt(logPort) === instancePort) {
            const errorKey = `${pattern}-${timestampMatch ? timestampMatch[1] : line.substring(0, 50)}`;
            if (!uniqueErrors.has(errorKey)) {
              uniqueErrors.add(errorKey);
              errors.push({ 
                line, 
                pattern, 
                immediate: true, 
                timestamp: timestampMatch ? timestampMatch[1] : null,
                port: logPort || instancePort
              });
            }
          }
        }
      }
      
      // 检查标准错误
      for (const pattern of STANDARD_ERROR_PATTERNS) {
        if (line.includes(pattern)) {
          // 只处理有端口信息且端口匹配当前实例的错误
          if (logPort && parseInt(logPort) === instancePort) {
            const errorKey = `${pattern}-${timestampMatch ? timestampMatch[1] : line.substring(0, 50)}`;
            if (!uniqueErrors.has(errorKey)) {
              uniqueErrors.add(errorKey);
              errors.push({ 
                line, 
                pattern, 
                immediate: false, 
                timestamp: timestampMatch ? timestampMatch[1] : null,
                port: logPort || instancePort
              });
            }
          }
        }
      }
    }
    
    if (errors.length > 0) {
      logger.debug(`${instance.name} (端口${instancePort}): 发现 ${errors.length} 个新错误（去重后）`);
    }
    
    return errors;
  } catch (e) {
    logger.error(`检查 ${instance.name} 错误日志失败: ${e}`);
    return [];
  }
}

async function handleApiInstanceError(instance, errors) {
  const immediateErrors = errors.filter(e => e.immediate);
  const standardErrors = errors.filter(e => !e.immediate);
  
  logger.warning(`${instance.name} (端口${instance.port}): 检测到 ${immediateErrors.length} 个严重错误，${standardErrors.length} 个标准错误`);
  
  // 检查错误计数
  const errorKey = instance.name;
  if (!errorCounts[errorKey]) {
    errorCounts[errorKey] = [];
  }
  
  const now = Date.now();
  
  // 只有当确实发现新错误时才增加计数
  if (errors.length > 0) {
    errorCounts[errorKey].push(now);
    logger.info(`${instance.name}: 新增 ${errors.length} 个错误记录，当前窗口内总错误数: ${errorCounts[errorKey].length}`);
  }
  
  // 清理过期的错误记录
  errorCounts[errorKey] = errorCounts[errorKey].filter(
    timestamp => now - timestamp < ERROR_WINDOW * 1000
  );
  
  const recentErrorCount = errorCounts[errorKey].length;
  
  // 判断是否需要采取行动
  const shouldTakeAction = immediateErrors.length > 0 || recentErrorCount >= ERROR_THRESHOLD;
  
  if (shouldTakeAction) {
    logger.info(`${instance.name} (端口${instance.port}): 触发错误处理流程`);
    
    // 获取新cookie
    const newCookie = getCookieFromPool();
    if (!newCookie) {
      logger.error(`${instance.name} (端口${instance.port}): 无法获取新cookie`);
      await sendMessage(
        `❌❌❌ Cookie池耗尽警告 ❌❌❌\n\n` +
        `⚠️ ${instance.name} - ${MACHINE_NAME}\n` +
        `端口: ${instance.port}\n` +
        `问题: Cookie池已空，无法获取新Cookie\n` +
        `建议: 请立即检查Cookie池状态并补充Cookie\n` +
        `时间: ${new Date().toLocaleString('zh-CN')}`,
        'error'
      );
      return;
    }
    
    // 更新配置
    if (!await updateApiConfig(instance, newCookie)) {
      addToCookiePool(newCookie);
      return;
    }
    
    // 重启服务
    logger.info(`${instance.name} (端口${instance.port}): 重启服务...`);
    try {
      await execAsync(`sudo systemctl restart ${instance.systemd_service}`);
      
      // 设置监控忽略期
      instanceMonitorIgnoreUntil[instance.name] = new Date(Date.now() + 90 * 1000);
      
      // 清空错误计数
      errorCounts[errorKey] = [];
      
      // 重置最后检查时间，避免重启后立即检测到旧错误
      lastCheckedTimestamp[instance.name] = new Date();
      
      // 发送通知
      const errorSummary = immediateErrors.length > 0
        ? `严重错误: ${immediateErrors[0].pattern}`
        : `错误频率过高 (${recentErrorCount}次/${ERROR_WINDOW}秒)`;
      
      await sendMessage(
        `❌❌❌ Cookie失效自动修复 ❌❌❌\n\n` +
        `🔧 ${instance.name} - ${MACHINE_NAME}\n` +
        `端口: ${instance.port}\n` +
        `问题: ${errorSummary}\n` +
        `操作: 已更换新Cookie并重启服务\n` +
        `状态: ✅ 修复完成，90秒后恢复监控\n` +
        `时间: ${new Date().toLocaleString('zh-CN')}`,
        'warning'
      );
      
    } catch (e) {
      logger.error(`${instance.name} (端口${instance.port}): 重启服务失败 - ${e}`);
      await sendMessage(
        `❌ ${instance.name} 重启失败\n\n` +
        `端口: ${instance.port}\n` +
        `错误: ${e.message}`,
        'error'
      );
    }
  }
}

async function apiMonitorThread() {
  logger.info("API监控线程已启动");

  let lastTrafficCheck = 0;

  while (apiMonitorRunning) {
    try {
      const now = new Date();

      // 定期检测流量耗尽（每2分钟检查一次）
      if (now.getTime() - lastTrafficCheck > 120000) {
        lastTrafficCheck = now.getTime();

        // 只在代理模式下检测流量耗尽
        const currentCommand = await getCurrentCommand();
        if (currentCommand && currentCommand !== 'direct' && isValidUrl(currentCommand)) {
          const isExhausted = await detectTrafficExhaustion();
          if (isExhausted) {
            logger.warning("⚠️ 流量耗尽检测触发，已自动切换到直连模式");
            // 跳过本轮API检查，让系统稳定
            await new Promise(resolve => setTimeout(resolve, 30000));
            continue;
          }
        }
      }

      for (const instance of api_instances) {
        // 检查是否在忽略期内
        const ignoreUntil = instanceMonitorIgnoreUntil[instance.name];
        if (ignoreUntil && now < ignoreUntil) {
          const remainingSeconds = Math.ceil((ignoreUntil - now) / 1000);
          logger.debug(`${instance.name}: 在忽略期内，剩余 ${remainingSeconds} 秒`);
          continue;
        }

        // 检查服务状态
        const isRunning = await checkInstanceStatus(instance);
        if (!isRunning) {
          logger.warning(`${instance.name}: 服务未响应`);
          continue;
        }

        // 检查错误
        logger.debug(`${instance.name}: 开始检查错误日志`);
        const errors = await checkApiErrors(instance);
        if (errors.length > 0) {
          logger.info(`${instance.name}: 发现 ${errors.length} 个错误，准备处理`);
          await handleApiInstanceError(instance, errors);
        } else {
          logger.debug(`${instance.name}: 未发现新错误`);
        }
      }

      // 等待下一次检查
      await new Promise(resolve => setTimeout(resolve, CHECK_INTERVAL * 1000));

    } catch (e) {
      logger.error(`API监控线程错误: ${e}`);
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }

  logger.info("API监控线程已停止");
}

//====================================================================
// 流量耗尽检测功能
//====================================================================

// 流量耗尽检测配置
const TRAFFIC_EXHAUSTION_CONFIG = {
  // 节点失效率阈值（80%）
  NODE_FAILURE_THRESHOLD: 0.8,
  // API错误率阈值（90%）
  API_ERROR_THRESHOLD: 0.9,
  // 延迟异常阈值（5000ms）
  LATENCY_TIMEOUT_THRESHOLD: 5000,
  // 连续失败时间阈值（5分钟）
  CONTINUOUS_FAILURE_DURATION: 5 * 60 * 1000,
  // 检测间隔（30秒）
  DETECTION_INTERVAL: 30 * 1000,
  // 最小检测节点数
  MIN_TEST_NODES: 10
};

// 流量耗尽检测状态
const trafficExhaustionState = {
  lastHealthyTime: new Date(),
  consecutiveFailures: 0,
  lastDetectionTime: null,
  isExhausted: false,
  detectionHistory: []
};

// 检测流量是否耗尽
async function detectTrafficExhaustion() {
  try {
    const now = new Date();

    // 避免频繁检测
    if (trafficExhaustionState.lastDetectionTime &&
        now - trafficExhaustionState.lastDetectionTime < TRAFFIC_EXHAUSTION_CONFIG.DETECTION_INTERVAL) {
      return trafficExhaustionState.isExhausted;
    }

    trafficExhaustionState.lastDetectionTime = now;

    logger.info("🔍 开始流量耗尽检测...");

    // 1. 检测节点批量失效
    const nodeFailureRate = await detectNodeBatchFailure();

    // 2. 检测API错误率激增
    const apiErrorRate = await detectApiErrorSpike();

    // 3. 检测延迟异常模式
    const latencyAbnormal = await detectLatencyAbnormality();

    // 综合判断
    const indicators = {
      nodeFailure: nodeFailureRate > TRAFFIC_EXHAUSTION_CONFIG.NODE_FAILURE_THRESHOLD,
      apiError: apiErrorRate > TRAFFIC_EXHAUSTION_CONFIG.API_ERROR_THRESHOLD,
      latencyAbnormal: latencyAbnormal,
      timestamp: now
    };

    // 记录检测历史
    trafficExhaustionState.detectionHistory.push(indicators);
    if (trafficExhaustionState.detectionHistory.length > 10) {
      trafficExhaustionState.detectionHistory.shift();
    }

    // 判断是否流量耗尽（至少满足2个指标）
    const positiveIndicators = Object.values(indicators).filter(v => v === true).length;
    const isCurrentlyExhausted = positiveIndicators >= 2;

    logger.info(`📊 流量耗尽检测结果:`);
    logger.info(`   节点失效率: ${(nodeFailureRate * 100).toFixed(1)}% (阈值: ${TRAFFIC_EXHAUSTION_CONFIG.NODE_FAILURE_THRESHOLD * 100}%)`);
    logger.info(`   API错误率: ${(apiErrorRate * 100).toFixed(1)}% (阈值: ${TRAFFIC_EXHAUSTION_CONFIG.API_ERROR_THRESHOLD * 100}%)`);
    logger.info(`   延迟异常: ${latencyAbnormal ? '是' : '否'}`);
    logger.info(`   满足指标数: ${positiveIndicators}/3`);

    if (isCurrentlyExhausted) {
      trafficExhaustionState.consecutiveFailures++;

      // 检查连续失败时间
      const failureDuration = now - trafficExhaustionState.lastHealthyTime;

      if (failureDuration > TRAFFIC_EXHAUSTION_CONFIG.CONTINUOUS_FAILURE_DURATION) {
        if (!trafficExhaustionState.isExhausted) {
          logger.error("🚨 检测到流量耗尽！准备切换到直连模式");
          trafficExhaustionState.isExhausted = true;

          // 发送警报
          await sendMessage(
            `🚨 流量耗尽警报 - ${MACHINE_NAME}\n\n` +
            `检测时间: ${now.toLocaleString('zh-CN')}\n` +
            `节点失效率: ${(nodeFailureRate * 100).toFixed(1)}%\n` +
            `API错误率: ${(apiErrorRate * 100).toFixed(1)}%\n` +
            `延迟异常: ${latencyAbnormal ? '是' : '否'}\n` +
            `连续失败时长: ${Math.round(failureDuration / 60000)}分钟\n\n` +
            `正在自动切换到直连模式...`,
            'error'
          );

          // 自动切换到直连模式
          await autoSwitchToDirect();
        }
      }
    } else {
      // 恢复健康状态
      if (trafficExhaustionState.isExhausted) {
        logger.info("✅ 网络状况已恢复正常");
        await sendMessage(
          `✅ 网络恢复正常 - ${MACHINE_NAME}\n\n` +
          `恢复时间: ${now.toLocaleString('zh-CN')}\n` +
          `系统已从流量耗尽状态恢复`,
          'info'
        );
      }

      trafficExhaustionState.lastHealthyTime = now;
      trafficExhaustionState.consecutiveFailures = 0;
      trafficExhaustionState.isExhausted = false;
    }

    return trafficExhaustionState.isExhausted;

  } catch (e) {
    logger.error(`流量耗尽检测失败: ${e.message}`);
    return false;
  }
}

// 检测节点批量失效
async function detectNodeBatchFailure() {
  try {
    const proxiesInfo = await getAllProxies();
    if (!proxiesInfo || !proxiesInfo.proxies) {
      return 0;
    }

    const groupInfo = proxiesInfo.proxies[PROXY_GROUPS[0]] || {};
    const allNodes = groupInfo.all || [];

    if (allNodes.length < TRAFFIC_EXHAUSTION_CONFIG.MIN_TEST_NODES) {
      return 0;
    }

    // 测试随机节点样本
    const sampleSize = Math.min(20, allNodes.length);
    const testNodes = [...allNodes].sort(() => Math.random() - 0.5).slice(0, sampleSize);

    let failedCount = 0;

    await Promise.all(
      testNodes.map(async (node) => {
        const latency = await testNodeLatency(node);
        if (latency <= 0 || latency > TRAFFIC_EXHAUSTION_CONFIG.LATENCY_TIMEOUT_THRESHOLD) {
          failedCount++;
        }
      })
    );

    return failedCount / testNodes.length;

  } catch (e) {
    logger.error(`节点批量失效检测失败: ${e.message}`);
    return 0;
  }
}

// 检测API错误率激增
async function detectApiErrorSpike() {
  try {
    let totalRequests = 0;
    let totalErrors = 0;

    // 统计最近的API访问情况
    for (const [port, stats] of Object.entries(accessStats)) {
      const total = stats.success + stats.errors;
      if (total > 0) {
        totalRequests += total;
        totalErrors += stats.errors;
      }
    }

    if (totalRequests === 0) {
      return 0;
    }

    return totalErrors / totalRequests;

  } catch (e) {
    logger.error(`API错误率检测失败: ${e.message}`);
    return 0;
  }
}

// 检测延迟异常模式
async function detectLatencyAbnormality() {
  try {
    const proxiesInfo = await getAllProxies();
    if (!proxiesInfo || !proxiesInfo.proxies) {
      return false;
    }

    const groupInfo = proxiesInfo.proxies[PROXY_GROUPS[0]] || {};
    const allNodes = groupInfo.all || [];

    if (allNodes.length < TRAFFIC_EXHAUSTION_CONFIG.MIN_TEST_NODES) {
      return false;
    }

    // 测试节点样本
    const sampleSize = Math.min(15, allNodes.length);
    const testNodes = [...allNodes].sort(() => Math.random() - 0.5).slice(0, sampleSize);

    let timeoutCount = 0;
    let validTests = 0;

    await Promise.all(
      testNodes.map(async (node) => {
        const latency = await testNodeLatency(node);
        validTests++;
        if (latency <= 0 || latency > TRAFFIC_EXHAUSTION_CONFIG.LATENCY_TIMEOUT_THRESHOLD) {
          timeoutCount++;
        }
      })
    );

    // 如果超过80%的节点都超时，认为是异常
    return validTests > 0 && (timeoutCount / validTests) > 0.8;

  } catch (e) {
    logger.error(`延迟异常检测失败: ${e.message}`);
    return false;
  }
}

// 自动切换到直连模式
async function autoSwitchToDirect() {
  try {
    logger.info("🔄 执行自动切换到直连模式...");

    const success = await switchToDirectMode();

    if (success) {
      logger.info("✅ 自动切换到直连模式成功");

      // 更新数据库命令为direct
      await updateDoesCommand('direct');
      await updateDoesMessage(`🔄 自动切换到直连模式 - ${new Date().toLocaleString('zh-CN')}`);

      await sendMessage(
        `✅ 自动切换成功 - ${MACHINE_NAME}\n\n` +
        `切换时间: ${new Date().toLocaleString('zh-CN')}\n` +
        `原因: 检测到流量耗尽\n` +
        `当前模式: 直连模式\n\n` +
        `所有API实例现在使用本机IP访问`,
        'info'
      );

    } else {
      logger.error("❌ 自动切换到直连模式失败");

      await sendMessage(
        `❌ 自动切换失败 - ${MACHINE_NAME}\n\n` +
        `切换时间: ${new Date().toLocaleString('zh-CN')}\n` +
        `原因: 流量耗尽检测触发\n` +
        `状态: 切换失败\n\n` +
        `请手动检查系统状态`,
        'error'
      );
    }

    return success;

  } catch (e) {
    logger.error(`自动切换到直连模式失败: ${e.message}`);
    return false;
  }
}

// 获取当前数据库命令
async function getCurrentCommand() {
  try {
    const { data, error } = await supabase
      .from('does')
      .select('command')
      .eq('name', MACHINE_NAME)
      .single();

    if (error) {
      logger.error(`获取当前命令失败: ${error.message}`);
      return null;
    }

    return data?.command || null;
  } catch (e) {
    logger.error(`获取当前命令异常: ${e.message}`);
    return null;
  }
}

// 更新数据库命令
async function updateDoesCommand(command) {
  try {
    const { error } = await supabase
      .from('does')
      .update({ command: command })
      .eq('name', MACHINE_NAME);

    if (error) {
      logger.error(`更新命令失败: ${error.message}`);
      return false;
    }

    logger.info(`✅ 已更新数据库命令为: ${command}`);
    return true;
  } catch (e) {
    logger.error(`更新命令异常: ${e.message}`);
    return false;
  }
}

//====================================================================
// 节点切换线程
//====================================================================

async function nodeSwitchThread() {
  logger.info("启动节点服务...");
  
  if (!await waitForApiReady()) {
    await sendMessage("❌ Singbox代理服务启动失败\n\n无法连接到Clash API，请检查服务是否正常运行", 'error');
    return;
  }
  
  // 获取初始IP信息
  await getAllProxyIps();
  
  // 获取初始节点信息
  const currentNodes = {};
  for (const group of PROXY_GROUPS) {
    currentNodes[group] = await getCurrentNode(group);
  }
  
  // 构建IP信息
  const ipInfo = [];
  for (let i = 0; i < PROXY_GROUPS.length; i++) {
    const group = PROXY_GROUPS[i];
    const port = PROXY_PORTS[i];
    const ip = proxyIps[port] || "未知";
    const location = proxyLocations[port] || "未知";
    ipInfo.push(`${group} (端口${port}): ${currentNodes[group]} - ${ip} (${location})`);
  }
  
  const initialMessage = `📡 Singbox代理服务状态 - ${MACHINE_NAME}\n\n` +
    `时间: ${new Date().toLocaleString('zh-CN')}\n` +
    `机器: ${MACHINE_NAME}\n\n` +
    `各代理组状态:\n${ipInfo.join('\n')}\n\n` +
    `监控API实例数: ${api_instances.length}\n\n` +
    `🔄 节点切换模式: 手动触发`;
  
  await sendMessage(initialMessage, 'info');
  
  // 节点切换需要手动触发
  logger.info("节点切换需要通过命令手动触发");
}


//====================================================================
// 定时任务
//====================================================================

async function generateStatusReport() {
  try {
    // 获取最新IP信息
    await getAllProxyIps();
    
    // 检查所有服务状态
    const singboxRunning = await checkSingboxRunning();
    
    const apiStatusList = [];
    const apiStatisticsList = [];
    
    for (const instance of api_instances) {
      const status = await checkInstanceStatus(instance) ? "✅ 运行中" : "❌ 未运行";
      const [proxyPort, ip, location] = getInstanceProxyInfo(instance);
      apiStatusList.push(`- ${instance.name} (端口 ${instance.port}): ${status}`);
      apiStatusList.push(`  代理: 端口${proxyPort} - ${ip} (${location})`);
      
      // 获取统计信息
      const stats = getInstanceStatisticsSummary(instance.name);
      apiStatisticsList.push(`\n📈 ${instance.name} 统计:`);
      apiStatisticsList.push(`  - 总请求数: ${stats.totalRequests}`);
      apiStatisticsList.push(`  - 成功率: ${stats.successRate}`);
      apiStatisticsList.push(`  - 每小时请求: ${stats.requestsPerHour}`);
      apiStatisticsList.push(`  - 近1小时成功率: ${stats.successRateLastHour}`);
      apiStatisticsList.push(`  - 平均响应时间: ${stats.avgResponseTime}`);
      apiStatisticsList.push(`  - 错误总数: ${stats.totalErrors}`);
    }
    
    const { stdout: uptime } = await execAsync("uptime");
    
    // 计算运行时长
    const runningTime = new Date() - apiStatistics.startTime;
    const hours = Math.floor(runningTime / 3600000);
    const minutes = Math.floor((runningTime % 3600000) / 60000);
    
    let statusMessage = `📊 状态报告 - ${MACHINE_NAME}\n\n`;
    statusMessage += `时间: ${new Date().toLocaleString('zh-CN')}\n`;
    statusMessage += `机器: ${MACHINE_NAME}\n`;
    statusMessage += `运行时长: ${hours}小时${minutes}分钟\n\n`;
    statusMessage += `代理组状态:\n`;
    
    for (let i = 0; i < PROXY_GROUPS.length; i++) {
      const group = PROXY_GROUPS[i];
      const port = PROXY_PORTS[i];
      const ip = proxyIps[port] || "未知";
      const location = proxyLocations[port] || "未知";
      const node = await getCurrentNode(group);
      statusMessage += `- ${group} (端口${port}): ${node} - ${ip} (${location})\n`;
    }
    
    statusMessage += `\n服务状态:\n`;
    statusMessage += `- Singbox代理: ${singboxRunning ? "✅ 运行中" : "❌ 未运行"}\n`;
    statusMessage += `- API服务状态:\n${apiStatusList.join('\n')}\n`;
    
    statusMessage += `\nAPI端口统计信息:${apiStatisticsList.join('\n')}\n\n`;
    
    statusMessage += `Cookie状态:\n`;
    statusMessage += `- Cookie池大小: ${cookiePool.length}/${COOKIE_POOL_SIZE}\n\n`;
    statusMessage += `订阅状态:\n`;
    statusMessage += `- 当前订阅: ${currentSubscriptionUrl ? currentSubscriptionUrl.substring(0, 50) + '...' : '未知'}\n\n`;
    statusMessage += `节点切换模式:\n`;
    statusMessage += `- 触发方式: 基于douyinlikes字段变化（非定时切换）\n`;
    statusMessage += `- 最后douyinlikes值: ${lastDouyinlikes}\n\n`;
    statusMessage += `系统信息:\n`;
    statusMessage += `- 系统负载: ${uptime.includes("load average:") ? uptime.split("load average:")[1].trim() : "未知"}`;
    
    logger.info("生成状态报告");
    await sendMessage(statusMessage, 'status');
    
    // 保存统计数据
    await saveStatistics();
    
  } catch (e) {
    logger.error(`生成状态报告时出错: ${e}`);
  }
}

async function scheduledCookiePoolRefresh() {
  if (cookiePoolLastRefresh && (Date.now() - cookiePoolLastRefresh.getTime()) < 3600000) {
    logger.info("cookie池刷新冷却中，跳过本次刷新");
    return;
  }
  
  if (cookiePool.length < COOKIE_POOL_SIZE) {
    logger.info("开始定时刷新cookie池...");
    const success = await refreshCookiePool();
    if (success) {
      cookiePoolLastRefresh = new Date();
      logger.info(`cookie池已刷新，当前大小: ${cookiePool.length}`);
    } else {
      logger.warning("定时刷新cookie池失败");
    }
  } else {
    logger.info(`cookie池已满(${COOKIE_POOL_SIZE})，跳过刷新`);
  }
}

async function scheduledNodeQualityCheck() {
  try {
    logger.info("执行定期节点质量检查...");
    if (await selectBestNodesForGroups()) {
      logger.info("定期节点质量检查完成，已优化节点选择");
      // 重新获取IP信息
      await getAllProxyIps();
      
      // 构建新的IP信息
      const ipInfo = [];
      for (const instance of api_instances) {
        const [proxyPort, ip, location] = getInstanceProxyInfo(instance);
        ipInfo.push(`${instance.name}: ${ip} (${location})`);
      }
      
      await sendMessage(
        `🔄 定期节点优化完成 - ${MACHINE_NAME}\n\n` +
        `时间: ${new Date().toLocaleString('zh-CN')}\n` +
        `机器: ${MACHINE_NAME}\n\n` +
        `新的代理IP配置:\n${ipInfo.join('\n')}`,
        'info'
      );
    } else {
      logger.warning("定期节点质量检查失败");
    }
  } catch (e) {
    logger.error(`定期节点质量检查时出错: ${e}`);
  }
}

//====================================================================
// 服务检查和初始化
//====================================================================

async function checkSingboxRunning() {
  try {
    const { stdout } = await execAsync("systemctl is-active singbox.service");
    return stdout.trim() === 'active';
  } catch (e) {
    return false;
  }
}

async function initializeServices() {
  logger.info("========================================");
  logger.info("集成监控服务启动 - 多实例版本V2 (JavaScript)");
  logger.info("========================================");
  
  // 初始化环境（包括创建服务文件和执行清理）
  await initializeEnvironment();
  
  // 生成环境报告
  const cleanupMessage = "✅ 环境初始化完成\n\n已检查并创建必要的systemd服务文件";
  await sendMessage(cleanupMessage, 'info');
  
  // 等待清理完成
  logger.info("等待环境清理生效 (5秒)...");
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // 获取初始代理IP信息
  await getAllProxyIps();
  
  // 构建IP信息
  const ipInfo = [];
  for (const instance of api_instances) {
    const [proxyPort, ip, location] = getInstanceProxyInfo(instance);
    ipInfo.push(`${instance.name}: 端口${proxyPort} - ${ip} (${location})`);
  }
  
  const startupMessage = `🚀 集成监控服务启动 - ${MACHINE_NAME}\n\n` +
    `时间: ${new Date().toLocaleString('zh-CN')}\n` +
    `机器: ${MACHINE_NAME}\n` +
    `监控实例数: ${api_instances.length}\n\n` +
    `各实例代理配置:\n${ipInfo.join('\n')}\n\n` +
    `正在执行启动初始化流程...`;
  
  await sendMessage(startupMessage, 'info');
  
  // 1. 重启所有服务
  logger.info("第1步: 重启所有服务（Singbox代理和所有API实例）");
  
  // 重启Singbox代理服务
  logger.info("正在重启Singbox代理服务...");
  try {
    await execAsync("sudo systemctl restart singbox.service");
    logger.info("Singbox代理服务重启成功");
  } catch (e) {
    logger.error(`Singbox代理服务重启失败: ${e}`);
  }
  
  // 等待代理服务启动
  logger.info("等待Singbox代理服务完全启动 (10秒)...");
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  // 重启所有API实例服务
  const uniqueServices = [...new Set(api_instances.map(instance => instance.systemd_service))];
  
  if (uniqueServices.length === 1) {
    // 所有实例使用同一个服务
    const serviceName = uniqueServices[0];
    logger.info(`所有API实例使用同一服务: ${serviceName}`);
    logger.info(`正在重启多端口API服务: ${serviceName}`);
    try {
      await execAsync(`sudo systemctl restart ${serviceName}`);
      logger.info(`多端口API服务 ${serviceName} 重启成功`);
    } catch (e) {
      logger.error(`多端口API服务 ${serviceName} 重启失败: ${e}`);
    }
  } else {
    // 多个独立服务
    for (const instance of api_instances) {
      const serviceName = instance.systemd_service;
      logger.info(`正在重启API服务: ${serviceName}`);
      try {
        await execAsync(`sudo systemctl restart ${serviceName}`);
        logger.info(`API服务 ${serviceName} 重启成功`);
      } catch (e) {
        logger.error(`API服务 ${serviceName} 重启失败: ${e}`);
      }
      // 短暂等待避免同时重启
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // 等待所有服务完全启动
  logger.info("等待所有API服务完全启动 (20秒)...");
  await new Promise(resolve => setTimeout(resolve, 20000));
  
  // 2. 清空并重新填充cookie池
  logger.info("第2步: 清空并重新填充cookie池");
  await resetAndFillCookiePool();
  
  // 3. 强制更换所有实例的cookie并重启API服务
  logger.info("第3步: 强制更换所有实例的cookie并重启API服务");
  
  // 获取所需数量的cookie
  logger.info(`从cookie池获取${api_instances.length}个新cookie...`);
  const cookiesForInstances = [];
  for (let i = 0; i < api_instances.length; i++) {
    const cookie = getCookieFromPool();
    if (cookie) {
      cookiesForInstances.push(cookie);
    } else {
      logger.error(`无法获取第${i + 1}个cookie`);
      // 将已获取的cookie放回池中
      cookiesForInstances.forEach(c => addToCookiePool(c));
      cookiesForInstances.length = 0;
      break;
    }
  }
  
  if (cookiesForInstances.length === api_instances.length) {
    // 更新每个实例的配置文件
    for (let i = 0; i < api_instances.length; i++) {
      const instance = api_instances[i];
      const instanceName = instance.name;
      logger.info(`${instanceName}: 更新配置文件...`);
      if (!await updateApiConfig(instance, cookiesForInstances[i])) {
        logger.error(`${instanceName}: 更新配置失败`);
      }
    }
    
    // 依次重启所有API服务
    for (const instance of api_instances) {
      const instanceName = instance.name;
      const serviceName = instance.systemd_service;
      logger.info(`${instanceName}: 重启服务 ${serviceName}...`);
      
      try {
        await execAsync(`sudo systemctl restart ${serviceName}`);
        logger.info(`${instanceName}: 重启命令执行成功`);
      } catch (e) {
        logger.error(`${instanceName}: 重启失败: ${e}`);
      }
      
      // 等待服务启动
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // 设置监控忽略期
      instanceMonitorIgnoreUntil[instanceName] = new Date(Date.now() + 90 * 1000);
      logger.info(`${instanceName}: 设置90秒监控忽略期`);
    }
  } else {
    logger.error("无法获取足够的cookie，跳过强制更新");
  }
  
  // 4. 等待服务完全启动
  logger.info("第4步: 等待所有服务完全启动 (30秒)...");
  await new Promise(resolve => setTimeout(resolve, 30000));
  
  // 5. 检查最终服务状态
  logger.info("第5步: 检查最终服务状态");
  const singboxRunning = await checkSingboxRunning();
  
  let apiRunningCount = 0;
  const apiStatusDetails = [];
  for (const instance of api_instances) {
    const [proxyPort, ip, location] = getInstanceProxyInfo(instance);
    if (await checkInstanceStatus(instance)) {
      apiRunningCount++;
      apiStatusDetails.push(`- ${instance.name}: ✅ 运行中 (代理端口${proxyPort} - ${ip})`);
    } else {
      apiStatusDetails.push(`- ${instance.name}: ❌ 未运行 (代理端口${proxyPort})`);
    }
  }
  
  // 6. 智能节点质量检测和优化
  logger.info("第6步: 智能节点质量检测和优化");
  if (singboxRunning) {
    if (await waitForApiReady()) {
      logger.info("开始智能节点优化...");
      if (await selectBestNodesForGroups()) {
        logger.info("智能节点优化完成");
        await sendMessage(
          `🎯 智能节点优化完成 - ${MACHINE_NAME}\n\n` +
          `时间: ${new Date().toLocaleString('zh-CN')}\n` +
          `机器: ${MACHINE_NAME}\n` +
          `已为所有4个代理组选择最优质的节点\n\n` +
          `正在更新代理IP信息...`,
          'info'
        );
        
        // 等待节点切换生效
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        // 重新获取IP信息
        await getAllProxyIps();
      } else {
        logger.warning("智能节点优化失败");
      }
    }
  }
  
  // 7. 等待代理服务就绪
  if (singboxRunning) {
    if (await waitForApiReady()) {
      logger.info("Singbox Clash API已就绪");
    } else {
      logger.warning("Singbox Clash API未就绪，但继续运行");
    }
  }
  
  // 8. 发送状态报告
  const initCompleteMessage = `✅ 初始化完成 - ${MACHINE_NAME}\n\n` +
    `时间: ${new Date().toLocaleString('zh-CN')}\n` +
    `机器: ${MACHINE_NAME}\n\n` +
    `初始化步骤:\n` +
    `- 全服务重启: ✅ 完成（Singbox + ${api_instances.length}个API实例）\n` +
    `- Cookie池重置: ✅ 完成 (${cookiePool.length}/${COOKIE_POOL_SIZE})\n` +
    `- Cookie分配: ✅ 完成（为${api_instances.length}个实例分配新cookie）\n` +
    `- 节点优化: ✅ 完成（智能选择最优质节点）\n\n` +
    `服务状态:\n` +
    `- Singbox代理: ${singboxRunning ? "✅ 运行中" : "❌ 未运行"}\n` +
    `- API实例状态 (${apiRunningCount}/${api_instances.length}):\n` +
    `${apiStatusDetails.join('\n')}\n` +
    `- 集成监控: ✅ 运行中\n\n` +
    `开始监控所有服务...`;
  
  await sendMessage(initCompleteMessage, 'info');
  
  return [singboxRunning, apiRunningCount > 0];
}

//====================================================================
// 主函数
//====================================================================

async function main() {
  try {
    // 设置机器名称
    MACHINE_NAME = await getMachineName();
    logger.info(`当前机器名称: ${MACHINE_NAME}`);
    
    // 加载cookie池
    loadCookiePool();
    
    // 加载历史统计数据
    loadStatistics();
    
    // 初始化所有实例的最后检查时间
    for (const instance of api_instances) {
      lastCheckedTimestamp[instance.name] = new Date();
    }
    logger.info("初始化所有实例的最后检查时间");
    
    // 设置命令监听器（这会根据command字段初始化模式）
    await setupCommandListener();
    
    // 如果不是direct模式，则更新订阅
    if (lastCommand !== 'direct') {
      await updateSubscriptionFromDatabase();
    }
    
    // 初始化所有服务
    await initializeServices();
    
    // 设置定时任务
    setInterval(generateStatusReport, 12 * 60 * 60 * 1000); // 每12小时
    setInterval(scheduledCookiePoolRefresh, 6 * 60 * 60 * 1000); // 每6小时
    setInterval(reportApiStatus, 60 * 1000); // 每分钟
    setInterval(scheduledNodeQualityCheck, 4 * 60 * 60 * 1000); // 每4小时
    
    logger.info("定时任务已设置：");
    logger.info("- 每12小时: 生成状态报告");
    logger.info("- 每6小时: 刷新cookie池");
    logger.info("- 每4小时: 节点质量检查和优化");
    logger.info("- 每1分钟: 报告API状态");
    
    // 启动各个线程
    nodeSwitchThread();
    apiMonitorThread();
    
    // 处理退出信号
    process.on('SIGINT', () => {
      logger.info("接收到中断信号，程序退出");
      cleanupAndExit();
    });
    
    process.on('SIGTERM', () => {
      logger.info("接收到终止信号，程序退出");
      cleanupAndExit();
    });
    
  } catch (e) {
    logger.error(`主程序错误: ${e}`);
    process.exit(1);
  }
}

// 启动主程序
main();