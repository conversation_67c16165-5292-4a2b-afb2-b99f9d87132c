#!/usr/bin/env node

/**
 * 迁移后配置检查脚本
 * 检查所有必要的配置和依赖是否正确设置
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

// 颜色定义
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// 日志函数
const log = {
    info: (msg) => console.log(`${colors.green}[INFO]${colors.reset} ${msg}`),
    warn: (msg) => console.log(`${colors.yellow}[WARN]${colors.reset} ${msg}`),
    error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
    step: (msg) => console.log(`${colors.blue}[STEP]${colors.reset} ${msg}`),
    success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
    fail: (msg) => console.log(`${colors.red}[FAIL]${colors.reset} ${msg}`)
};

// 检查结果统计
const checkResults = {
    passed: 0,
    failed: 0,
    warnings: 0
};

// 执行命令并返回结果
function execCommand(command, silent = true) {
    try {
        const result = execSync(command, { 
            encoding: 'utf8',
            stdio: silent ? 'pipe' : 'inherit'
        });
        return { success: true, output: result.trim() };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

// 检查文件是否存在
function checkFileExists(filePath, description) {
    log.step(`检查 ${description}...`);
    
    if (fs.existsSync(filePath)) {
        log.success(`✅ ${description} 存在: ${filePath}`);
        checkResults.passed++;
        return true;
    } else {
        log.fail(`❌ ${description} 不存在: ${filePath}`);
        checkResults.failed++;
        return false;
    }
}

// 检查命令是否可用
function checkCommand(command, description) {
    log.step(`检查 ${description}...`);
    
    const result = execCommand(`which ${command}`);
    if (result.success) {
        const version = execCommand(`${command} --version`);
        log.success(`✅ ${description} 可用: ${result.output}`);
        if (version.success) {
            log.info(`   版本: ${version.output.split('\n')[0]}`);
        }
        checkResults.passed++;
        return true;
    } else {
        log.fail(`❌ ${description} 不可用`);
        checkResults.failed++;
        return false;
    }
}

// 检查systemd服务
function checkSystemdService(serviceName, description) {
    log.step(`检查 ${description}...`);
    
    const statusResult = execCommand(`systemctl is-enabled ${serviceName}`);
    const activeResult = execCommand(`systemctl is-active ${serviceName}`);
    
    if (statusResult.success && statusResult.output === 'enabled') {
        log.success(`✅ ${description} 已启用`);
        
        if (activeResult.success && activeResult.output === 'active') {
            log.success(`✅ ${description} 正在运行`);
            checkResults.passed++;
            return true;
        } else {
            log.warn(`⚠️ ${description} 已启用但未运行`);
            checkResults.warnings++;
            return false;
        }
    } else {
        log.fail(`❌ ${description} 未启用`);
        checkResults.failed++;
        return false;
    }
}

// 检查端口是否可用
function checkPort(port, description) {
    log.step(`检查端口 ${port} (${description})...`);
    
    const result = execCommand(`lsof -i :${port}`);
    if (result.success) {
        log.success(`✅ 端口 ${port} 正在使用 (${description})`);
        checkResults.passed++;
        return true;
    } else {
        log.warn(`⚠️ 端口 ${port} 未使用 (${description})`);
        checkResults.warnings++;
        return false;
    }
}

// 检查配置文件内容
function checkConfigFile(filePath, checks, description) {
    log.step(`检查 ${description}...`);
    
    if (!fs.existsSync(filePath)) {
        log.fail(`❌ ${description} 文件不存在: ${filePath}`);
        checkResults.failed++;
        return false;
    }
    
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let allPassed = true;
        
        for (const check of checks) {
            if (check.type === 'contains') {
                if (content.includes(check.value)) {
                    log.success(`✅ ${check.description}`);
                } else {
                    log.fail(`❌ ${check.description}`);
                    allPassed = false;
                }
            } else if (check.type === 'json') {
                try {
                    const json = JSON.parse(content);
                    if (check.path) {
                        const value = check.path.split('.').reduce((obj, key) => obj?.[key], json);
                        if (value !== undefined) {
                            log.success(`✅ ${check.description}: ${value}`);
                        } else {
                            log.fail(`❌ ${check.description}: 未找到`);
                            allPassed = false;
                        }
                    }
                } catch (e) {
                    log.fail(`❌ ${check.description}: JSON解析失败`);
                    allPassed = false;
                }
            }
        }
        
        if (allPassed) {
            checkResults.passed++;
        } else {
            checkResults.failed++;
        }
        
        return allPassed;
    } catch (error) {
        log.fail(`❌ 读取 ${description} 失败: ${error.message}`);
        checkResults.failed++;
        return false;
    }
}

// 获取机器信息
function getMachineInfo() {
    log.step('获取机器信息...');
    
    const hostname = os.hostname();
    const machineId = fs.existsSync('/etc/machine-id') ? 
        fs.readFileSync('/etc/machine-id', 'utf8').trim() : 'unknown';
    
    const networkResult = execCommand('ip link show');
    let macAddress = 'unknown';
    if (networkResult.success) {
        const match = networkResult.output.match(/ether ([a-f0-9:]{17})/);
        if (match) macAddress = match[1];
    }
    
    const ipResult = execCommand('hostname -I');
    const ipAddress = ipResult.success ? ipResult.output.split(' ')[0] : 'unknown';
    
    console.log('\n=== 机器特征信息 ===');
    console.log(`主机名: ${hostname}`);
    console.log(`Machine ID: ${machineId}`);
    console.log(`MAC地址: ${macAddress}`);
    console.log(`IP地址: ${ipAddress}`);
    
    return { hostname, machineId, macAddress, ipAddress };
}

// 主检查函数
async function runChecks() {
    console.log('🔍 开始迁移后配置检查...\n');
    
    // 1. 检查基础命令
    checkCommand('node', 'Node.js');
    checkCommand('npm', 'NPM');
    checkCommand('sing-box', 'sing-box');
    checkCommand('jq', 'jq');
    checkCommand('lsof', 'lsof');
    
    // 2. 检查项目文件
    const projectRoot = path.join(__dirname, '..');
    checkFileExists(path.join(projectRoot, 'scripts/integrated_monitor_multi.js'), '集成监控脚本');
    checkFileExists(path.join(projectRoot, 'config/integrated_config_multi.yaml'), '集成配置文件');
    checkFileExists(path.join(projectRoot, 'config/config.json'), 'sing-box配置文件');
    
    // 3. 检查systemd服务
    checkSystemdService('singbox.service', 'sing-box服务');
    checkSystemdService('integrated-monitor-multi-js.service', '集成监控服务');
    
    // 4. 检查端口使用情况
    checkPort(9090, 'sing-box Clash API');
    checkPort(1080, '代理端口 proxy0');
    checkPort(1081, '代理端口 proxy1');
    checkPort(1082, '代理端口 proxy2');
    checkPort(1083, '代理端口 proxy3');
    checkPort(8080, 'API实例0');
    checkPort(8081, 'API实例1');
    checkPort(8082, 'API实例2');
    checkPort(8083, 'API实例3');
    
    // 5. 检查配置文件内容
    checkConfigFile(
        path.join(projectRoot, 'config/config.json'),
        [
            { type: 'json', path: 'inbounds.0.listen_port', description: 'sing-box监听端口配置' },
            { type: 'json', path: 'outbounds.0.type', description: 'outbound类型配置' },
            { type: 'contains', value: '"direct"', description: 'direct出站配置' }
        ],
        'sing-box配置文件内容'
    );
    
    // 6. 检查环境变量
    log.step('检查环境变量...');
    const envVars = ['SUPABASE_URL', 'SUPABASE_KEY'];
    let envOk = true;
    
    for (const envVar of envVars) {
        if (process.env[envVar]) {
            log.success(`✅ ${envVar} 已设置`);
        } else {
            log.warn(`⚠️ ${envVar} 未设置`);
            envOk = false;
        }
    }
    
    if (envOk) {
        checkResults.passed++;
    } else {
        checkResults.warnings++;
    }
    
    // 7. 获取机器信息
    const machineInfo = getMachineInfo();
    
    // 8. 生成报告
    console.log('\n=== 检查结果汇总 ===');
    console.log(`✅ 通过: ${checkResults.passed}`);
    console.log(`⚠️ 警告: ${checkResults.warnings}`);
    console.log(`❌ 失败: ${checkResults.failed}`);
    
    if (checkResults.failed === 0) {
        log.success('\n🎉 所有关键检查都通过了！系统已准备就绪。');
        
        if (checkResults.warnings > 0) {
            log.warn('有一些警告项目，但不影响基本功能。');
        }
        
        console.log('\n📋 下一步操作:');
        console.log('1. 确保在数据库does表中添加了此机器的记录');
        console.log('2. 在integrated_monitor_multi.js的KNOWN_MACHINES中添加机器配置');
        console.log('3. 重启服务: sudo systemctl restart integrated-monitor-multi-js.service');
        
    } else {
        log.error('\n❌ 有关键检查失败，请先解决这些问题。');
    }
    
    return checkResults.failed === 0;
}

// 运行检查
if (require.main === module) {
    runChecks().catch(console.error);
}

module.exports = { runChecks, getMachineInfo };
