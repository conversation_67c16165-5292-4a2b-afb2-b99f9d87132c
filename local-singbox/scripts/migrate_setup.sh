#!/bin/bash

# 项目迁移自动化脚本
# 用于将整个项目迁移到新机器并自动配置所有依赖

set -e

echo "🚀 开始项目迁移设置..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要以root用户运行此脚本"
        exit 1
    fi
}

# 检查系统要求
check_system() {
    log_step "检查系统要求..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法检测操作系统版本"
        exit 1
    fi
    
    source /etc/os-release
    if [[ "$ID" != "ubuntu" ]]; then
        log_warn "此脚本主要为Ubuntu设计，其他系统可能需要手动调整"
    fi
    
    log_info "系统检查完成: $PRETTY_NAME"
}

# 安装系统依赖
install_system_deps() {
    log_step "安装系统依赖..."
    
    sudo apt update
    sudo apt install -y \
        curl \
        wget \
        lsof \
        net-tools \
        jq \
        unzip \
        git \
        build-essential \
        python3 \
        python3-pip
    
    log_info "系统依赖安装完成"
}

# 安装Node.js
install_nodejs() {
    log_step "安装Node.js..."
    
    # 检查是否已安装nvm
    if [[ ! -d "$HOME/.nvm" ]]; then
        log_info "安装NVM..."
        curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
        
        # 重新加载bash配置
        export NVM_DIR="$HOME/.nvm"
        [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
        [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
    fi
    
    # 安装Node.js 18.20.4
    log_info "安装Node.js 18.20.4..."
    nvm install 18.20.4
    nvm use 18.20.4
    nvm alias default 18.20.4
    
    # 验证安装
    node_version=$(node --version)
    npm_version=$(npm --version)
    log_info "Node.js版本: $node_version"
    log_info "NPM版本: $npm_version"
}

# 安装项目依赖
install_project_deps() {
    log_step "安装项目依赖..."
    
    # 进入项目目录
    cd "$(dirname "$0")/.."
    
    # 安装npm依赖
    if [[ -f "package.json" ]]; then
        log_info "安装NPM依赖..."
        npm install
    else
        log_warn "未找到package.json，跳过NPM依赖安装"
    fi
    
    # 安装Python依赖（如果需要）
    if [[ -f "requirements.txt" ]]; then
        log_info "安装Python依赖..."
        pip3 install -r requirements.txt
    fi
}

# 下载sing-box
install_singbox() {
    log_step "安装sing-box..."
    
    # 检查是否已安装
    if command -v sing-box &> /dev/null; then
        log_info "sing-box已安装，版本: $(sing-box version)"
        return
    fi
    
    # 下载最新版本
    log_info "下载sing-box..."
    SINGBOX_VERSION="1.8.10"
    ARCH=$(uname -m)
    
    case $ARCH in
        x86_64)
            ARCH_NAME="amd64"
            ;;
        aarch64)
            ARCH_NAME="arm64"
            ;;
        *)
            log_error "不支持的架构: $ARCH"
            exit 1
            ;;
    esac
    
    DOWNLOAD_URL="https://github.com/SagerNet/sing-box/releases/download/v${SINGBOX_VERSION}/sing-box-${SINGBOX_VERSION}-linux-${ARCH_NAME}.tar.gz"
    
    cd /tmp
    wget -O sing-box.tar.gz "$DOWNLOAD_URL"
    tar -xzf sing-box.tar.gz
    
    # 安装到系统路径
    sudo cp "sing-box-${SINGBOX_VERSION}-linux-${ARCH_NAME}/sing-box" /usr/local/bin/
    sudo chmod +x /usr/local/bin/sing-box
    
    # 验证安装
    log_info "sing-box安装完成，版本: $(sing-box version)"
    
    # 清理临时文件
    rm -rf /tmp/sing-box*
}

# 创建systemd服务
create_systemd_services() {
    log_step "创建systemd服务..."
    
    PROJECT_DIR=$(pwd)
    
    # 创建sing-box服务
    sudo tee /etc/systemd/system/singbox.service > /dev/null <<EOF
[Unit]
Description=sing-box service
Documentation=https://sing-box.sagernet.org
After=network.target nss-lookup.target

[Service]
CapabilityBoundingSet=CAP_NET_ADMIN CAP_NET_BIND_SERVICE CAP_SYS_PTRACE CAP_DAC_READ_SEARCH
AmbientCapabilities=CAP_NET_ADMIN CAP_NET_BIND_SERVICE CAP_SYS_PTRACE CAP_DAC_READ_SEARCH
ExecStart=/usr/local/bin/sing-box run -c $PROJECT_DIR/local-singbox/config/config.json
ExecReload=/bin/kill -HUP \$MAINPID
Restart=on-failure
RestartSec=10s
LimitNOFILE=infinity

[Install]
WantedBy=multi-user.target
EOF

    # 创建集成监控服务
    sudo tee /etc/systemd/system/integrated-monitor-multi-js.service > /dev/null <<EOF
[Unit]
Description=Integrated Monitor Service (Multi-Instance JavaScript Version)
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=$PROJECT_DIR/local-singbox/scripts
ExecStart=/home/<USER>/.nvm/versions/node/v18.20.4/bin/node $PROJECT_DIR/local-singbox/scripts/integrated_monitor_multi.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd
    sudo systemctl daemon-reload
    
    # 启用服务
    sudo systemctl enable singbox.service
    sudo systemctl enable integrated-monitor-multi-js.service
    
    log_info "systemd服务创建完成"
}

# 配置环境变量
setup_environment() {
    log_step "配置环境变量..."
    
    # 检查是否存在环境配置文件
    ENV_FILE="$HOME/.env"
    
    if [[ ! -f "$ENV_FILE" ]]; then
        log_warn "未找到.env文件，请手动配置以下环境变量："
        echo "SUPABASE_URL=your_supabase_url"
        echo "SUPABASE_KEY=your_supabase_key"
        echo "DOES_TABLE_ID=1"
        echo ""
        echo "您可以创建 $ENV_FILE 文件并添加这些变量"
    else
        log_info "找到环境配置文件: $ENV_FILE"
    fi
}

# 获取机器特征信息
get_machine_info() {
    log_step "获取机器特征信息..."
    
    HOSTNAME=$(hostname)
    MACHINE_ID=$(cat /etc/machine-id 2>/dev/null || echo "unknown")
    MAC_ADDRESS=$(ip link show | awk '/ether/ {print $2; exit}' 2>/dev/null || echo "unknown")
    IP_ADDRESS=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "unknown")
    
    echo ""
    log_info "=== 机器特征信息 ==="
    echo "主机名: $HOSTNAME"
    echo "Machine ID: $MACHINE_ID"
    echo "MAC地址: $MAC_ADDRESS"
    echo "IP地址: $IP_ADDRESS"
    echo ""
    
    log_warn "请将以下信息添加到数据库的does表中："
    echo "INSERT INTO does (name, command, message) VALUES ('$HOSTNAME', 'direct', 'New machine added');"
    echo ""
    log_warn "同时需要在integrated_monitor_multi.js的KNOWN_MACHINES中添加此机器的配置"
}

# 主函数
main() {
    echo "🎯 项目迁移自动化脚本"
    echo "================================"
    
    check_root
    check_system
    install_system_deps
    install_nodejs
    install_project_deps
    install_singbox
    create_systemd_services
    setup_environment
    get_machine_info
    
    echo ""
    log_info "=== 迁移完成 ==="
    echo "1. 请配置Supabase环境变量"
    echo "2. 请在数据库中添加新机器记录"
    echo "3. 请更新KNOWN_MACHINES配置"
    echo "4. 运行: sudo systemctl start integrated-monitor-multi-js.service"
    echo ""
    log_info "所有功能将在重启服务后自动激活！"
}

# 运行主函数
main "$@"
